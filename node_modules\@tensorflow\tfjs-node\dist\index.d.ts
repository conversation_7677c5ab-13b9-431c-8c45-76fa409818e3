/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import './register_all_kernels';
import * as tf from '@tensorflow/tfjs';
import * as nodeIo from './io/index';
export declare const version: {
    'tfjs-node': string;
    'tfjs-core': string;
    'tfjs-backend-cpu': string;
    'tfjs-backend-webgl': string;
    'tfjs-data': string;
    'tfjs-layers': string;
    'tfjs-converter': string;
    tfjs: string;
};
export declare const io: {
    fileSystem: typeof nodeIo.fileSystem;
    nodeHTTPRequest: typeof nodeIo.nodeHTTPRequest;
    copyModel: typeof import("@tensorflow/tfjs-core/dist/io/model_management").copyModel;
    listModels: typeof import("@tensorflow/tfjs-core/dist/io/model_management").listModels;
    moveModel: typeof import("@tensorflow/tfjs-core/dist/io/model_management").moveModel;
    removeModel: typeof import("@tensorflow/tfjs-core/dist/io/model_management").removeModel;
    browserFiles: typeof import("@tensorflow/tfjs-core/dist/io/browser_files").browserFiles;
    browserHTTPRequest: typeof import("@tensorflow/tfjs-core/dist/io/http").browserHTTPRequest;
    CompositeArrayBuffer: typeof import("@tensorflow/tfjs-core/dist/io/composite_array_buffer").CompositeArrayBuffer;
    concatenateArrayBuffers: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").concatenateArrayBuffers;
    decodeWeights: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").decodeWeights;
    decodeWeightsStream: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").decodeWeightsStream;
    encodeWeights: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").encodeWeights;
    fromMemory: typeof import("@tensorflow/tfjs-core/dist/io/passthrough").fromMemory;
    fromMemorySync: typeof import("@tensorflow/tfjs-core/dist/io/passthrough").fromMemorySync;
    getLoadHandlers: (url: string | string[], loadOptions?: tf.io.LoadOptions) => tf.io.IOHandler[];
    getModelArtifactsForJSON: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").getModelArtifactsForJSON;
    getModelArtifactsForJSONSync: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").getModelArtifactsForJSONSync;
    getModelArtifactsInfoForJSON: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").getModelArtifactsInfoForJSON;
    getSaveHandlers: (url: string | string[]) => tf.io.IOHandler[];
    getWeightSpecs: typeof import("@tensorflow/tfjs-core/dist/io/io_utils").getWeightSpecs;
    http: typeof import("@tensorflow/tfjs-core/dist/io/http").http;
    isHTTPScheme: typeof import("@tensorflow/tfjs-core/dist/io/http").isHTTPScheme;
    loadWeights: typeof import("@tensorflow/tfjs-core/dist/io/weights_loader").loadWeights;
    registerLoadRouter: (loudRouter: import("@tensorflow/tfjs-core/dist/io/router_registry").IORouter) => void;
    registerSaveRouter: (loudRouter: import("@tensorflow/tfjs-core/dist/io/router_registry").IORouter) => void;
    weightsLoaderFactory: typeof import("@tensorflow/tfjs-core/dist/io/weights_loader").weightsLoaderFactory;
    withSaveHandler: typeof import("@tensorflow/tfjs-core/dist/io/passthrough").withSaveHandler;
    withSaveHandlerSync: typeof import("@tensorflow/tfjs-core/dist/io/passthrough").withSaveHandlerSync;
};
export * from '@tensorflow/tfjs';
export * from './node';
