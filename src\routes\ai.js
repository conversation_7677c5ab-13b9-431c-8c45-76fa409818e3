const express = require('express');
const AIController = require('../controllers/aiController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     RoutePrediction:
 *       type: object
 *       properties:
 *         latitude:
 *           type: number
 *           description: Predicted latitude
 *         longitude:
 *           type: number
 *           description: Predicted longitude
 *         estimatedTime:
 *           type: string
 *           format: date-time
 *           description: Estimated arrival time
 *         confidence:
 *           type: number
 *           minimum: 0
 *           maximum: 1
 *           description: Prediction confidence score
 *         timeHorizon:
 *           type: number
 *           description: Prediction time horizon in minutes
 *     AnomalyAnalysis:
 *       type: object
 *       properties:
 *         isAnomaly:
 *           type: boolean
 *           description: Whether anomaly was detected
 *         confidence:
 *           type: number
 *           minimum: 0
 *           maximum: 1
 *           description: Anomaly confidence score
 *         anomalies:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [speed, location, time, route]
 *               score:
 *                 type: number
 *               details:
 *                 type: object
 *         threshold:
 *           type: number
 *           description: Anomaly detection threshold
 */

/**
 * @swagger
 * /api/ai/devices/{id}/predict-route:
 *   get:
 *     summary: Get route prediction for a device
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *       - in: query
 *         name: timeHorizon
 *         schema:
 *           type: integer
 *           minimum: 5
 *           maximum: 180
 *           default: 30
 *         description: Prediction time horizon in minutes
 *     responses:
 *       200:
 *         description: Route prediction generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     device:
 *                       type: object
 *                     prediction:
 *                       $ref: '#/components/schemas/RoutePrediction'
 *                     metadata:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.get('/devices/:id/predict-route',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  [
    ValidationMiddleware.query('timeHorizon')
      .optional()
      .isInt({ min: 5, max: 180 })
      .withMessage('Time horizon must be between 5 and 180 minutes'),
    ValidationMiddleware.handleValidationErrors
  ],
  AIController.getRoutePrediction
);

/**
 * @swagger
 * /api/ai/devices/{id}/analyze-anomaly:
 *   post:
 *     summary: Analyze location for anomalies
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - latitude
 *               - longitude
 *             properties:
 *               latitude:
 *                 type: number
 *                 minimum: -90
 *                 maximum: 90
 *               longitude:
 *                 type: number
 *                 minimum: -180
 *                 maximum: 180
 *               speed:
 *                 type: number
 *                 minimum: 0
 *               heading:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 360
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       200:
 *         description: Anomaly analysis completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     device:
 *                       type: object
 *                     location:
 *                       type: object
 *                     analysis:
 *                       $ref: '#/components/schemas/AnomalyAnalysis'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.post('/devices/:id/analyze-anomaly',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  [
    ValidationMiddleware.body('latitude')
      .isFloat({ min: -90, max: 90 })
      .withMessage('Latitude must be between -90 and 90'),
    ValidationMiddleware.body('longitude')
      .isFloat({ min: -180, max: 180 })
      .withMessage('Longitude must be between -180 and 180'),
    ValidationMiddleware.body('speed')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Speed must be a positive number'),
    ValidationMiddleware.body('heading')
      .optional()
      .isFloat({ min: 0, max: 360 })
      .withMessage('Heading must be between 0 and 360'),
    ValidationMiddleware.body('timestamp')
      .optional()
      .isISO8601()
      .withMessage('Timestamp must be a valid ISO 8601 date'),
    ValidationMiddleware.handleValidationErrors
  ],
  AIController.analyzeLocationAnomaly
);

/**
 * @swagger
 * /api/ai/devices/{id}/insights:
 *   get:
 *     summary: Get AI insights for a device
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 7
 *         description: Number of days for analysis
 *     responses:
 *       200:
 *         description: Device insights generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     device:
 *                       type: object
 *                     period:
 *                       type: object
 *                     insights:
 *                       type: object
 *                       properties:
 *                         dataPoints:
 *                           type: integer
 *                         movement:
 *                           type: object
 *                         behavior:
 *                           type: object
 *                         efficiency:
 *                           type: object
 *                         recommendations:
 *                           type: array
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.get('/devices/:id/insights',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  [
    ValidationMiddleware.query('days')
      .optional()
      .isInt({ min: 1, max: 365 })
      .withMessage('Days must be between 1 and 365'),
    ValidationMiddleware.handleValidationErrors
  ],
  AIController.getDeviceInsights
);

/**
 * @swagger
 * /api/ai/devices/{id}/movement-patterns:
 *   get:
 *     summary: Get movement patterns for a device
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 7
 *           maximum: 365
 *           default: 30
 *         description: Number of days for pattern analysis
 *     responses:
 *       200:
 *         description: Movement patterns analyzed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     device:
 *                       type: object
 *                     period:
 *                       type: object
 *                     patterns:
 *                       type: object
 *                       properties:
 *                         frequentLocations:
 *                           type: array
 *                         routePatterns:
 *                           type: array
 *                         timePatterns:
 *                           type: object
 *                         anomalousEvents:
 *                           type: array
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.get('/devices/:id/movement-patterns',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  [
    ValidationMiddleware.query('days')
      .optional()
      .isInt({ min: 7, max: 365 })
      .withMessage('Days must be between 7 and 365'),
    ValidationMiddleware.handleValidationErrors
  ],
  AIController.getMovementPatterns
);

module.exports = router;
