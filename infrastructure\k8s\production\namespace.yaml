apiVersion: v1
kind: Namespace
metadata:
  name: gps-tracker-prod
  labels:
    name: gps-tracker-prod
    environment: production
    app: enterprise-gps-tracker
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: gps-tracker-quota
  namespace: gps-tracker-prod
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: gps-tracker-limits
  namespace: gps-tracker-prod
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
