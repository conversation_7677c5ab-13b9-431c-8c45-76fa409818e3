const jwt = require('jsonwebtoken');
const passport = require('passport');
const JwtStrategy = require('passport-jwt').Strategy;
const ExtractJwt = require('passport-jwt').ExtractJwt;
const OAuth2Strategy = require('passport-oauth2');
const config = require('config');
const logger = require('../utils/logger');
const database = require('../utils/database');

/**
 * Enterprise Authentication Middleware with OAuth2, RBAC, and Multi-tenant support
 */
class AuthenticationManager {
  constructor() {
    this.setupPassportStrategies();
    this.rateLimitStore = new Map();
  }

  /**
   * Setup Passport.js strategies
   */
  setupPassportStrategies() {
    const authConfig = config.get('auth');

    // JWT Strategy
    passport.use(new JwtStrategy({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: authConfig.jwt.secret,
      issuer: authConfig.jwt.issuer,
      audience: authConfig.jwt.audience,
      algorithms: [authConfig.jwt.algorithm],
      passReqToCallback: true
    }, async (req, payload, done) => {
      try {
        // Check if token is blacklisted
        const isBlacklisted = await this.isTokenBlacklisted(payload.jti);
        if (isBlacklisted) {
          return done(null, false, { message: 'Token has been revoked' });
        }

        // Get user from database
        const user = await this.getUserById(payload.sub);
        if (!user) {
          return done(null, false, { message: 'User not found' });
        }

        // Check if user is active
        if (!user.isActive) {
          return done(null, false, { message: 'User account is deactivated' });
        }

        // Check tenant access if multi-tenant
        if (payload.tenant && !this.hasAccessToTenant(user, payload.tenant)) {
          return done(null, false, { message: 'Access denied to tenant' });
        }

        // Add request context
        req.user = user;
        req.tenant = payload.tenant;
        req.tokenPayload = payload;

        return done(null, user);
      } catch (error) {
        logger.security('JWT validation failed', { 
          error: error.message, 
          userId: payload.sub,
          ip: req.ip 
        });
        return done(error, false);
      }
    }));

    // Google OAuth2 Strategy
    if (authConfig.oauth2.google.clientId) {
      passport.use('google', new OAuth2Strategy({
        authorizationURL: 'https://accounts.google.com/o/oauth2/v2/auth',
        tokenURL: 'https://oauth2.googleapis.com/token',
        clientID: authConfig.oauth2.google.clientId,
        clientSecret: authConfig.oauth2.google.clientSecret,
        callbackURL: authConfig.oauth2.google.callbackURL,
        scope: ['openid', 'profile', 'email']
      }, async (accessToken, refreshToken, profile, done) => {
        try {
          const user = await this.handleOAuthUser(profile, 'google');
          return done(null, user);
        } catch (error) {
          return done(error, null);
        }
      }));
    }

    // Microsoft OAuth2 Strategy
    if (authConfig.oauth2.microsoft.clientId) {
      passport.use('microsoft', new OAuth2Strategy({
        authorizationURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        tokenURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
        clientID: authConfig.oauth2.microsoft.clientId,
        clientSecret: authConfig.oauth2.microsoft.clientSecret,
        callbackURL: authConfig.oauth2.microsoft.callbackURL,
        scope: ['openid', 'profile', 'email']
      }, async (accessToken, refreshToken, profile, done) => {
        try {
          const user = await this.handleOAuthUser(profile, 'microsoft');
          return done(null, user);
        } catch (error) {
          return done(error, null);
        }
      }));
    }
  }

  /**
   * JWT Authentication Middleware
   */
  authenticate(options = {}) {
    return async (req, res, next) => {
      try {
        // Extract token
        const token = this.extractToken(req);
        if (!token) {
          return this.sendUnauthorized(res, 'No token provided');
        }

        // Verify token
        const payload = await this.verifyToken(token);
        if (!payload) {
          return this.sendUnauthorized(res, 'Invalid token');
        }

        // Get user
        const user = await this.getUserById(payload.sub);
        if (!user || !user.isActive) {
          return this.sendUnauthorized(res, 'User not found or inactive');
        }

        // Check permissions if required
        if (options.permissions && !this.hasPermissions(user, options.permissions)) {
          return this.sendForbidden(res, 'Insufficient permissions');
        }

        // Check tenant access
        if (options.tenant && !this.hasAccessToTenant(user, options.tenant)) {
          return this.sendForbidden(res, 'Access denied to tenant');
        }

        // Add to request
        req.user = user;
        req.tenant = payload.tenant;
        req.tokenPayload = payload;

        // Log successful authentication
        logger.security('Authentication successful', {
          userId: user.id,
          username: user.username,
          role: user.role,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });

        next();
      } catch (error) {
        logger.security('Authentication failed', {
          error: error.message,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        return this.sendUnauthorized(res, 'Authentication failed');
      }
    };
  }

  /**
   * API Key Authentication Middleware
   */
  authenticateApiKey(options = {}) {
    return async (req, res, next) => {
      try {
        const authConfig = config.get('auth.apiKeys');
        
        // Extract API key
        const apiKey = req.get(authConfig.headerName) || req.query[authConfig.queryParam];
        if (!apiKey) {
          return this.sendUnauthorized(res, 'API key required');
        }

        // Validate API key
        const keyData = await this.validateApiKey(apiKey);
        if (!keyData) {
          return this.sendUnauthorized(res, 'Invalid API key');
        }

        // Check rate limits for API key
        if (await this.isApiKeyRateLimited(apiKey)) {
          return this.sendTooManyRequests(res, 'API key rate limit exceeded');
        }

        // Get associated user/device
        const entity = await this.getEntityByApiKey(keyData);
        if (!entity || !entity.isActive) {
          return this.sendUnauthorized(res, 'API key owner not found or inactive');
        }

        // Add to request
        req.apiKey = keyData;
        req.user = entity.type === 'user' ? entity : null;
        req.device = entity.type === 'device' ? entity : null;

        // Log API key usage
        logger.security('API key authentication successful', {
          apiKeyId: keyData.id,
          entityType: entity.type,
          entityId: entity.id,
          ip: req.ip
        });

        next();
      } catch (error) {
        logger.security('API key authentication failed', {
          error: error.message,
          ip: req.ip
        });
        return this.sendUnauthorized(res, 'API key authentication failed');
      }
    };
  }

  /**
   * Role-Based Access Control (RBAC) Middleware
   */
  authorize(requiredRoles = [], requiredPermissions = []) {
    return (req, res, next) => {
      try {
        const user = req.user;
        if (!user) {
          return this.sendUnauthorized(res, 'Authentication required');
        }

        // Check roles
        if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
          logger.security('Authorization failed - insufficient role', {
            userId: user.id,
            userRole: user.role,
            requiredRoles,
            ip: req.ip
          });
          return this.sendForbidden(res, 'Insufficient role permissions');
        }

        // Check permissions
        if (requiredPermissions.length > 0 && !this.hasPermissions(user, requiredPermissions)) {
          logger.security('Authorization failed - insufficient permissions', {
            userId: user.id,
            userRole: user.role,
            requiredPermissions,
            ip: req.ip
          });
          return this.sendForbidden(res, 'Insufficient permissions');
        }

        next();
      } catch (error) {
        logger.error('Authorization error', { error: error.message });
        return this.sendForbidden(res, 'Authorization failed');
      }
    };
  }

  /**
   * Multi-tenant Access Control
   */
  requireTenant(tenantParam = 'tenantId') {
    return async (req, res, next) => {
      try {
        const tenantId = req.params[tenantParam] || req.query[tenantParam] || req.body[tenantParam];
        
        if (!tenantId) {
          return this.sendBadRequest(res, 'Tenant ID required');
        }

        // Check if user has access to tenant
        if (!this.hasAccessToTenant(req.user, tenantId)) {
          logger.security('Tenant access denied', {
            userId: req.user.id,
            requestedTenant: tenantId,
            userTenants: req.user.tenants,
            ip: req.ip
          });
          return this.sendForbidden(res, 'Access denied to tenant');
        }

        req.tenant = tenantId;
        next();
      } catch (error) {
        logger.error('Tenant authorization error', { error: error.message });
        return this.sendForbidden(res, 'Tenant authorization failed');
      }
    };
  }

  /**
   * Rate Limiting for Authentication Endpoints
   */
  authRateLimit(maxAttempts = 5, windowMs = 15 * 60 * 1000) {
    return (req, res, next) => {
      const key = `auth_rate_limit:${req.ip}:${req.body.email || req.body.username || 'unknown'}`;
      const now = Date.now();
      
      // Clean old entries
      this.cleanRateLimitStore(windowMs);
      
      // Get current attempts
      const attempts = this.rateLimitStore.get(key) || [];
      const recentAttempts = attempts.filter(timestamp => now - timestamp < windowMs);
      
      if (recentAttempts.length >= maxAttempts) {
        const oldestAttempt = Math.min(...recentAttempts);
        const resetTime = Math.ceil((oldestAttempt + windowMs - now) / 1000);
        
        logger.rateLimit(req.ip, req.originalUrl, maxAttempts, windowMs);
        
        return res.status(429).json({
          success: false,
          message: 'Too many authentication attempts',
          retryAfter: resetTime
        });
      }
      
      // Add current attempt
      recentAttempts.push(now);
      this.rateLimitStore.set(key, recentAttempts);
      
      next();
    };
  }

  /**
   * Extract JWT token from request
   */
  extractToken(req) {
    const authHeader = req.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return req.query.token || req.body.token;
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token) {
    try {
      const authConfig = config.get('auth.jwt');
      const payload = jwt.verify(token, authConfig.secret, {
        issuer: authConfig.issuer,
        audience: authConfig.audience,
        algorithms: [authConfig.algorithm]
      });
      
      return payload;
    } catch (error) {
      logger.security('Token verification failed', { error: error.message });
      return null;
    }
  }

  /**
   * Check if token is blacklisted
   */
  async isTokenBlacklisted(jti) {
    try {
      const redis = database.getRedis();
      const result = await redis.get(`blacklist:${jti}`);
      return !!result;
    } catch (error) {
      logger.error('Blacklist check failed', { error: error.message });
      return false;
    }
  }

  /**
   * Check user permissions
   */
  hasPermissions(user, requiredPermissions) {
    const authConfig = config.get('auth.rbac');
    if (!authConfig.enabled) return true;
    
    const userPermissions = authConfig.permissions[user.role] || [];
    
    // Admin has all permissions
    if (userPermissions.includes('*')) return true;
    
    // Check each required permission
    return requiredPermissions.every(permission => {
      return userPermissions.some(userPerm => {
        if (userPerm === permission) return true;
        if (userPerm.endsWith('*')) {
          const prefix = userPerm.slice(0, -1);
          return permission.startsWith(prefix);
        }
        return false;
      });
    });
  }

  /**
   * Check tenant access
   */
  hasAccessToTenant(user, tenantId) {
    if (!user.tenants || user.tenants.length === 0) return true; // No tenant restrictions
    return user.tenants.includes(tenantId);
  }

  /**
   * Get user by ID (implement based on your user model)
   */
  async getUserById(userId) {
    // This should be implemented to fetch user from your database
    // For now, returning a mock implementation
    try {
      // const User = require('../models/User');
      // return await User.findById(userId);
      return null; // Placeholder
    } catch (error) {
      logger.error('Failed to get user by ID', { userId, error: error.message });
      return null;
    }
  }

  /**
   * Validate API key
   */
  async validateApiKey(apiKey) {
    try {
      // Implement API key validation logic
      // This could involve decryption, database lookup, etc.
      return null; // Placeholder
    } catch (error) {
      logger.error('API key validation failed', { error: error.message });
      return null;
    }
  }

  /**
   * Handle OAuth user
   */
  async handleOAuthUser(profile, provider) {
    try {
      // Implement OAuth user handling
      // Create or update user based on OAuth profile
      return null; // Placeholder
    } catch (error) {
      logger.error('OAuth user handling failed', { provider, error: error.message });
      throw error;
    }
  }

  /**
   * Clean rate limit store
   */
  cleanRateLimitStore(windowMs) {
    const now = Date.now();
    for (const [key, attempts] of this.rateLimitStore.entries()) {
      const validAttempts = attempts.filter(timestamp => now - timestamp < windowMs);
      if (validAttempts.length === 0) {
        this.rateLimitStore.delete(key);
      } else {
        this.rateLimitStore.set(key, validAttempts);
      }
    }
  }

  /**
   * Send unauthorized response
   */
  sendUnauthorized(res, message) {
    return res.status(401).json({
      success: false,
      message,
      code: 'UNAUTHORIZED'
    });
  }

  /**
   * Send forbidden response
   */
  sendForbidden(res, message) {
    return res.status(403).json({
      success: false,
      message,
      code: 'FORBIDDEN'
    });
  }

  /**
   * Send bad request response
   */
  sendBadRequest(res, message) {
    return res.status(400).json({
      success: false,
      message,
      code: 'BAD_REQUEST'
    });
  }

  /**
   * Send too many requests response
   */
  sendTooManyRequests(res, message) {
    return res.status(429).json({
      success: false,
      message,
      code: 'TOO_MANY_REQUESTS'
    });
  }
}

// Create singleton instance
const authManager = new AuthenticationManager();

module.exports = {
  authenticate: authManager.authenticate.bind(authManager),
  authenticateApiKey: authManager.authenticateApiKey.bind(authManager),
  authorize: authManager.authorize.bind(authManager),
  requireTenant: authManager.requireTenant.bind(authManager),
  authRateLimit: authManager.authRateLimit.bind(authManager),
  passport
};
