const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');

/**
 * Device Management Service
 */
class DeviceService {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3002;
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(compression());
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        service: 'device-service',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Device routes
    this.app.get('/devices', (req, res) => {
      res.json({
        success: true,
        message: 'Device service is running',
        data: []
      });
    });

    this.app.post('/devices', (req, res) => {
      res.json({
        success: true,
        message: 'Device created successfully',
        data: { id: 'device-' + Date.now() }
      });
    });
  }

  setupErrorHandling() {
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: 'Endpoint not found'
      });
    });

    this.app.use((error, req, res, next) => {
      console.error('Device service error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    });
  }

  start() {
    this.server = this.app.listen(this.port, () => {
      console.log(`📱 Device Service running on port ${this.port}`);
    });
  }
}

const service = new DeviceService();
service.start();
