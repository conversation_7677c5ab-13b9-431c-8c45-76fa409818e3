/**
 * Authentication Manager
 */
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.initializeAuth();
        this.bindEvents();
    }

    /**
     * Initialize authentication
     */
    async initializeAuth() {
        const token = localStorage.getItem('authToken');
        
        if (token) {
            try {
                api.setToken(token);
                const response = await api.getProfile();
                this.currentUser = response.data.user;
                this.showDashboard();
            } catch (error) {
                console.error('Token validation failed:', error);
                this.logout();
            }
        } else {
            this.showLogin();
        }
    }

    /**
     * Bind authentication events
     */
    bindEvents() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Register form
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // Modal toggles
        const showRegisterLink = document.getElementById('show-register');
        const showLoginLink = document.getElementById('show-login');
        
        if (showRegisterLink) {
            showRegisterLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showRegister();
            });
        }

        if (showLoginLink) {
            showLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLogin();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }
    }

    /**
     * Handle login form submission
     * @param {Event} e - Form submit event
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const email = formData.get('email');
        const password = formData.get('password');

        try {
            this.showLoading(true);
            
            const response = await api.login(email, password);
            
            if (response.success) {
                const { user, tokens } = response.data;
                
                // Store token and user data
                api.setToken(tokens.accessToken);
                this.currentUser = user;
                
                // Show success message
                this.showMessage('Login successful!', 'success');
                
                // Redirect to dashboard
                setTimeout(() => {
                    this.showDashboard();
                }, 1000);
            }
        } catch (error) {
            this.showMessage(error.message || 'Login failed', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Handle register form submission
     * @param {Event} e - Form submit event
     */
    async handleRegister(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const userData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            username: formData.get('username'),
            email: formData.get('email'),
            password: formData.get('password'),
            phone: formData.get('phone')
        };

        try {
            this.showLoading(true);
            
            const response = await api.register(userData);
            
            if (response.success) {
                const { user, tokens } = response.data;
                
                // Store token and user data
                api.setToken(tokens.accessToken);
                this.currentUser = user;
                
                // Show success message
                this.showMessage('Registration successful!', 'success');
                
                // Redirect to dashboard
                setTimeout(() => {
                    this.showDashboard();
                }, 1000);
            }
        } catch (error) {
            this.showMessage(error.message || 'Registration failed', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            await api.logout();
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            // Clear local data
            api.setToken(null);
            this.currentUser = null;
            
            // Show login modal
            this.showLogin();
        }
    }

    /**
     * Show login modal
     */
    showLogin() {
        this.hideAllModals();
        const loginModal = document.getElementById('login-modal');
        if (loginModal) {
            loginModal.classList.add('active');
        }
        
        // Hide dashboard
        const dashboardContainer = document.querySelector('.dashboard-container');
        if (dashboardContainer) {
            dashboardContainer.style.display = 'none';
        }
    }

    /**
     * Show register modal
     */
    showRegister() {
        this.hideAllModals();
        const registerModal = document.getElementById('register-modal');
        if (registerModal) {
            registerModal.classList.add('active');
        }
    }

    /**
     * Show dashboard
     */
    showDashboard() {
        this.hideAllModals();
        
        // Show dashboard
        const dashboardContainer = document.querySelector('.dashboard-container');
        if (dashboardContainer) {
            dashboardContainer.style.display = 'flex';
        }
        
        // Update user info
        this.updateUserInfo();
        
        // Initialize dashboard if not already done
        if (window.dashboard) {
            window.dashboard.initialize();
        }
    }

    /**
     * Hide all modals
     */
    hideAllModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.classList.remove('active');
        });
    }

    /**
     * Update user info in sidebar
     */
    updateUserInfo() {
        if (this.currentUser) {
            const usernameElement = document.getElementById('username');
            if (usernameElement) {
                usernameElement.textContent = this.currentUser.username || 'User';
            }
        }
    }

    /**
     * Show loading overlay
     * @param {boolean} show - Whether to show loading
     */
    showLoading(show) {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            if (show) {
                loadingOverlay.classList.add('active');
            } else {
                loadingOverlay.classList.remove('active');
            }
        }
    }

    /**
     * Show message to user
     * @param {string} message - Message text
     * @param {string} type - Message type (success, error, info)
     */
    showMessage(message, type = 'info') {
        // Create message element
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        // Style the message
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '5px',
            color: 'white',
            fontWeight: '500',
            zIndex: '9999',
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
        });
        
        // Set background color based on type
        switch (type) {
            case 'success':
                messageEl.style.backgroundColor = '#27ae60';
                break;
            case 'error':
                messageEl.style.backgroundColor = '#e74c3c';
                break;
            case 'warning':
                messageEl.style.backgroundColor = '#f39c12';
                break;
            default:
                messageEl.style.backgroundColor = '#3498db';
        }
        
        // Add to page
        document.body.appendChild(messageEl);
        
        // Remove after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 5000);
    }

    /**
     * Get current user
     * @returns {Object|null} Current user object
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Check if user is authenticated
     * @returns {boolean} Authentication status
     */
    isAuthenticated() {
        return !!this.currentUser;
    }
}

// Create global auth manager instance
window.auth = new AuthManager();
