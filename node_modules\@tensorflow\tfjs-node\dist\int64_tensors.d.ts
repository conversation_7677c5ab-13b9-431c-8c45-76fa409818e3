/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Shape } from '@tensorflow/tfjs';
/**
 * Node.js-specific tensor type: int64-type scalar.
 *
 * This class is created for a specific purpose: to support
 * writing `step`s to TensorBoard via op-kernel bindings.
 * `step` is required to have an int64 dtype, but TensorFlow.js
 * (tfjs-core) doesn't have a built-in int64 dtype. This is
 * related to a lack of `Int64Array` or `Uint64Array` typed
 * array in basic JavaScript.
 *
 * This class is introduced as a workaround.
 */
export declare class Int64Scalar {
    readonly value: number;
    readonly dtype: string;
    readonly rank: number;
    private valueArray_;
    private static endiannessOkay_;
    constructor(value: number);
    get shape(): Shape;
    /** Get the Int32Array that represents the int64 value. */
    get valueArray(): Int32Array;
}
/**
 * This method encodes a Int32Array as Int64 layout in order to create TF_INT64
 * tensor through binding.
 */
export declare function encodeInt32ArrayAsInt64(value: Int32Array): Int32Array;
