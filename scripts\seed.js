#!/usr/bin/env node

const mongoose = require('mongoose');
const config = require('config');
const logger = require('../shared/utils/logger');

/**
 * Database seeding script for development and testing
 */
class DatabaseSeeder {
  constructor() {
    this.seedData = {
      users: [
        {
          username: 'admin',
          email: '<EMAIL>',
          password: 'Admin123!',
          role: 'admin',
          profile: {
            firstName: 'System',
            lastName: 'Administrator'
          },
          isVerified: true
        },
        {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'Test123!',
          role: 'manager',
          profile: {
            firstName: 'Test',
            lastName: 'User'
          },
          isVerified: true
        }
      ],
      devices: [
        {
          deviceId: 'DEMO001',
          name: 'Demo Vehicle 1',
          type: 'vehicle',
          category: 'fleet',
          hardware: {
            manufacturer: 'Demo Corp',
            model: 'GPS-2000'
          }
        },
        {
          deviceId: 'DEMO002',
          name: 'Demo Asset Tracker',
          type: 'asset',
          category: 'logistics',
          hardware: {
            manufacturer: 'Demo Corp',
            model: 'ASSET-100'
          }
        }
      ]
    };
  }

  async connect() {
    try {
      const mongoUri = config.get('database.mongodb.uri');
      await mongoose.connect(mongoUri, config.get('database.mongodb.options'));
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error.message);
      process.exit(1);
    }
  }

  async disconnect() {
    await mongoose.connection.close();
    console.log('✅ Disconnected from MongoDB');
  }

  async clearDatabase() {
    console.log('🧹 Clearing existing data...');
    
    const collections = await mongoose.connection.db.listCollections().toArray();
    
    for (const collection of collections) {
      await mongoose.connection.db.collection(collection.name).deleteMany({});
    }
    
    console.log('✅ Database cleared');
  }

  async seedUsers() {
    console.log('👥 Seeding users...');
    
    const User = require('../shared/models/User');
    
    for (const userData of this.seedData.users) {
      try {
        const existingUser = await User.findOne({ email: userData.email });
        
        if (!existingUser) {
          const user = new User(userData);
          await user.save();
          console.log(`  ✅ Created user: ${userData.email}`);
        } else {
          console.log(`  ⏭️  User already exists: ${userData.email}`);
        }
      } catch (error) {
        console.error(`  ❌ Failed to create user ${userData.email}:`, error.message);
      }
    }
  }

  async seedDevices() {
    console.log('📱 Seeding devices...');
    
    const Device = require('../shared/models/Device');
    const User = require('../shared/models/User');
    
    // Get admin user as device owner
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.error('❌ Admin user not found, skipping device seeding');
      return;
    }

    for (const deviceData of this.seedData.devices) {
      try {
        const existingDevice = await Device.findOne({ deviceId: deviceData.deviceId });
        
        if (!existingDevice) {
          const device = new Device({
            ...deviceData,
            owner: adminUser._id
          });
          await device.save();
          console.log(`  ✅ Created device: ${deviceData.deviceId}`);
        } else {
          console.log(`  ⏭️  Device already exists: ${deviceData.deviceId}`);
        }
      } catch (error) {
        console.error(`  ❌ Failed to create device ${deviceData.deviceId}:`, error.message);
      }
    }
  }

  async seedSampleLocations() {
    console.log('📍 Seeding sample locations...');
    
    const Location = require('../shared/models/Location');
    const Device = require('../shared/models/Device');
    
    const devices = await Device.find({});
    
    if (devices.length === 0) {
      console.log('  ⏭️  No devices found, skipping location seeding');
      return;
    }

    // Sample locations around New York City
    const sampleLocations = [
      { lat: 40.7128, lng: -74.0060 }, // NYC
      { lat: 40.7589, lng: -73.9851 }, // Times Square
      { lat: 40.6892, lng: -74.0445 }, // Statue of Liberty
      { lat: 40.7505, lng: -73.9934 }, // Empire State Building
      { lat: 40.7614, lng: -73.9776 }  // Central Park
    ];

    for (const device of devices) {
      for (let i = 0; i < sampleLocations.length; i++) {
        const location = sampleLocations[i];
        const timestamp = new Date(Date.now() - (sampleLocations.length - i) * 60000); // 1 minute intervals
        
        try {
          const locationDoc = new Location({
            device: device._id,
            location: {
              type: 'Point',
              coordinates: [location.lng, location.lat]
            },
            timestamp,
            speed: Math.random() * 60, // Random speed 0-60 km/h
            heading: Math.random() * 360, // Random heading
            accuracy: 5 + Math.random() * 10, // 5-15m accuracy
            deviceStatus: {
              batteryLevel: 70 + Math.random() * 30, // 70-100%
              signalStrength: -60 - Math.random() * 40 // -60 to -100 dBm
            }
          });
          
          await locationDoc.save();
        } catch (error) {
          console.error(`  ❌ Failed to create location for device ${device.deviceId}:`, error.message);
        }
      }
      
      console.log(`  ✅ Created sample locations for device: ${device.deviceId}`);
    }
  }

  async seed() {
    console.log('🌱 Starting database seeding...\n');

    await this.connect();

    try {
      // Only clear in development environment
      if (process.env.NODE_ENV === 'development') {
        await this.clearDatabase();
      }

      await this.seedUsers();
      await this.seedDevices();
      await this.seedSampleLocations();

      console.log('\n🎉 Database seeding completed successfully!');
      
    } catch (error) {
      console.error('❌ Seeding failed:', error.message);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }
}

// Run seeding
const seeder = new DatabaseSeeder();
seeder.seed().catch(error => {
  console.error('Seeding failed:', error);
  process.exit(1);
});
