/**
 * Package.json validation tests
 */
describe('Package Configuration', () => {
  let packageJson;

  beforeAll(() => {
    packageJson = require('../../package.json');
  });

  describe('Basic Package Info', () => {
    test('should have required fields', () => {
      expect(packageJson.name).toBe('enterprise-ai-gps-tracker');
      expect(packageJson.version).toBe('2.0.0');
      expect(packageJson.description).toBeDefined();
      expect(packageJson.author).toBeDefined();
      expect(packageJson.license).toBe('MIT');
    });

    test('should have valid engine requirements', () => {
      expect(packageJson.engines).toBeDefined();
      expect(packageJson.engines.node).toBe('>=18.0.0');
      expect(packageJson.engines.npm).toBe('>=9.0.0');
    });
  });

  describe('Scripts', () => {
    test('should have essential scripts', () => {
      const scripts = packageJson.scripts;
      
      expect(scripts.start).toBeDefined();
      expect(scripts.dev).toBeDefined();
      expect(scripts.test).toBeDefined();
      expect(scripts.lint).toBeDefined();
      expect(scripts['health:check']).toBeDefined();
    });

    test('should have development service scripts', () => {
      const scripts = packageJson.scripts;
      
      expect(scripts['dev:auth']).toBeDefined();
      expect(scripts['dev:device']).toBeDefined();
      expect(scripts['dev:location']).toBeDefined();
      expect(scripts['dev:ai']).toBeDefined();
      expect(scripts['dev:gateway']).toBeDefined();
      expect(scripts['dev:notification']).toBeDefined();
    });

    test('should have deployment scripts', () => {
      const scripts = packageJson.scripts;
      
      expect(scripts['deploy:staging']).toBeDefined();
      expect(scripts['deploy:production']).toBeDefined();
    });
  });

  describe('Dependencies', () => {
    test('should have core dependencies', () => {
      const deps = packageJson.dependencies;
      
      expect(deps.express).toBeDefined();
      expect(deps.mongoose).toBeDefined();
      expect(deps.redis).toBeDefined();
      expect(deps.ioredis).toBeDefined();
      expect(deps['socket.io']).toBeDefined();
      expect(deps.jsonwebtoken).toBeDefined();
    });

    test('should have AI/ML dependencies', () => {
      const deps = packageJson.dependencies;
      
      expect(deps['@tensorflow/tfjs']).toBeDefined();
      expect(deps['@tensorflow/tfjs-node']).toBeDefined();
      expect(deps['brain.js']).toBeDefined();
      expect(deps['ml-matrix']).toBeDefined();
      expect(deps['simple-statistics']).toBeDefined();
    });

    test('should have security dependencies', () => {
      const deps = packageJson.dependencies;
      
      expect(deps.helmet).toBeDefined();
      expect(deps.cors).toBeDefined();
      expect(deps['express-rate-limit']).toBeDefined();
      expect(deps['express-validator']).toBeDefined();
      expect(deps.bcryptjs).toBeDefined();
    });

    test('should have monitoring dependencies', () => {
      const deps = packageJson.dependencies;
      
      expect(deps['prom-client']).toBeDefined();
      expect(deps['jaeger-client']).toBeDefined();
      expect(deps.winston).toBeDefined();
    });
  });

  describe('Dev Dependencies', () => {
    test('should have testing dependencies', () => {
      const devDeps = packageJson.devDependencies;
      
      expect(devDeps.jest).toBeDefined();
      expect(devDeps.supertest).toBeDefined();
      expect(devDeps['mongodb-memory-server']).toBeDefined();
      expect(devDeps['ioredis-mock']).toBeDefined();
    });

    test('should have linting dependencies', () => {
      const devDeps = packageJson.devDependencies;
      
      expect(devDeps.eslint).toBeDefined();
      expect(devDeps.prettier).toBeDefined();
      expect(devDeps['lint-staged']).toBeDefined();
      expect(devDeps.husky).toBeDefined();
    });

    test('should have TypeScript support', () => {
      const devDeps = packageJson.devDependencies;
      
      expect(devDeps.typescript).toBeDefined();
      expect(devDeps['ts-node']).toBeDefined();
      expect(devDeps['@types/node']).toBeDefined();
    });
  });

  describe('Configuration', () => {
    test('should have Jest configuration', () => {
      expect(packageJson.jest).toBeDefined();
      expect(packageJson.jest.testEnvironment).toBe('node');
      expect(packageJson.jest.coverageDirectory).toBe('coverage');
    });

    test('should have lint-staged configuration', () => {
      expect(packageJson['lint-staged']).toBeDefined();
      expect(packageJson['lint-staged']['*.js']).toBeDefined();
    });
  });

  describe('Repository Info', () => {
    test('should have repository information', () => {
      expect(packageJson.repository).toBeDefined();
      expect(packageJson.repository.type).toBe('git');
      expect(packageJson.repository.url).toBeDefined();
    });

    test('should have bug tracking', () => {
      expect(packageJson.bugs).toBeDefined();
      expect(packageJson.bugs.url).toBeDefined();
    });

    test('should have homepage', () => {
      expect(packageJson.homepage).toBeDefined();
    });
  });

  describe('Keywords', () => {
    test('should have relevant keywords', () => {
      const keywords = packageJson.keywords;
      
      expect(keywords).toContain('gps');
      expect(keywords).toContain('tracking');
      expect(keywords).toContain('ai');
      expect(keywords).toContain('microservices');
      expect(keywords).toContain('enterprise');
    });
  });
});
