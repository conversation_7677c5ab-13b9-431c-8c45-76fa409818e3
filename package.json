{"name": "enterprise-ai-gps-tracker", "version": "2.0.0", "description": "Production-level AI-powered GPS tracking system with microservices architecture", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "dev:services": "concurrently \"npm run dev:auth\" \"npm run dev:device\" \"npm run dev:location\" \"npm run dev:ai\" \"npm run dev:gateway\"", "dev:auth": "nodemon services/auth-service/src/server.js", "dev:device": "nodemon services/device-service/src/server.js", "dev:location": "nodemon services/location-service/src/server.js", "dev:ai": "nodemon services/ai-service/src/server.js", "dev:gateway": "nodemon services/api-gateway/src/server.js", "test": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "build": "npm run build:services", "build:services": "npm run build:auth && npm run build:device && npm run build:location && npm run build:ai", "build:auth": "cd services/auth-service && npm run build", "build:device": "cd services/device-service && npm run build", "build:location": "cd services/location-service && npm run build", "build:ai": "cd services/ai-service && npm run build", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "benchmark": "node scripts/benchmark.js", "docs": "swagger-jsdoc -d swaggerDef.js -o docs/swagger.json src/**/*.js services/**/*.js", "security:audit": "npm audit && snyk test", "performance:test": "artillery run tests/performance/load-test.yml", "deploy:staging": "npm run build && npm run docker:build && kubectl apply -f k8s/staging/", "deploy:production": "npm run build && npm run docker:build && kubectl apply -f k8s/production/"}, "keywords": ["gps", "tracking", "ai", "microservices", "enterprise", "real-time", "geospatial", "mongodb", "nodejs", "production"], "author": "Enterprise GPS Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.2", "socket.io": "^4.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-oauth2": "^1.7.0", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "winston-elasticsearch": "^0.17.4", "dotenv": "^16.3.1", "config": "^3.3.9", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "lodash": "^4.17.21", "uuid": "^9.0.1", "nanoid": "^5.0.4", "joi": "^17.11.0", "ajv": "^8.12.0", "geolib": "^3.3.4", "turf": "^4.7.2", "@turf/turf": "^6.5.0", "ml-matrix": "^6.10.7", "ml-regression": "^6.0.1", "simple-statistics": "^7.8.3", "tensorflow": "^4.15.0", "@tensorflow/tfjs-node": "^4.15.0", "brain.js": "^2.0.0-beta.23", "node-cron": "^3.0.3", "agenda": "^5.0.0", "nodemailer": "^6.9.7", "twilio": "^4.19.3", "aws-sdk": "^2.1498.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "sharp": "^0.33.1", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "pdfkit": "^0.14.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-openapi-validator": "^5.1.2", "http-proxy-middleware": "^2.0.6", "express-circuit-breaker": "^0.0.1", "prom-client": "^15.1.0", "jaeger-client": "^3.19.0", "newrelic": "^11.7.0", "datadog-metrics": "^0.9.3", "elastic-apm-node": "^4.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2", "jest": "^29.7.0", "supertest": "^6.3.3", "mongodb-memory-server": "^9.1.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-security": "^1.7.1", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "snyk": "^1.1266.0", "artillery": "^2.0.3", "k6": "^0.0.0", "docker-compose": "^0.24.6", "@types/node": "^20.10.5", "typescript": "^5.3.3", "ts-node": "^10.9.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/company/enterprise-ai-gps-tracker.git"}, "bugs": {"url": "https://github.com/company/enterprise-ai-gps-tracker/issues"}, "homepage": "https://github.com/company/enterprise-ai-gps-tracker#readme", "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js", "services/**/*.js", "!**/node_modules/**", "!**/coverage/**"], "testMatch": ["**/tests/**/*.test.js", "**/tests/**/*.spec.js"]}}