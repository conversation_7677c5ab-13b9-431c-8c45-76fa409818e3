{"name": "enterprise-ai-gps-tracker", "version": "2.0.0", "description": "Production-level AI-powered GPS tracking system with microservices architecture", "main": "src/app.js", "scripts": {"start": "cross-env NODE_ENV=production node services/api-gateway/src/server.js", "dev": "cross-env NODE_ENV=development nodemon services/api-gateway/src/server.js", "dev:services": "concurrently --kill-others-on-fail \"npm run dev:auth\" \"npm run dev:device\" \"npm run dev:location\" \"npm run dev:ai\" \"npm run dev:gateway\" \"npm run dev:notification\"", "dev:auth": "cross-env NODE_ENV=development PORT=3001 nodemon services/auth-service/src/server.js", "dev:device": "cross-env NODE_ENV=development PORT=3002 nodemon services/device-service/src/server.js", "dev:location": "cross-env NODE_ENV=development PORT=3003 nodemon services/location-service/src/server.js", "dev:ai": "cross-env NODE_ENV=development PORT=3004 nodemon services/ai-service/src/server.js", "dev:gateway": "cross-env NODE_ENV=development PORT=3000 nodemon services/api-gateway/src/server.js", "dev:notification": "cross-env NODE_ENV=development PORT=3005 nodemon services/notification-service/src/server.js", "test": "cross-env NODE_ENV=test jest --coverage --detectOpenHandles --forceExit", "test:unit": "cross-env NODE_ENV=test jest --testPathPattern=unit --detectOpenHandles --forceExit", "test:integration": "cross-env NODE_ENV=test jest --testPathPattern=integration --detectOpenHandles --forceExit", "test:e2e": "cross-env NODE_ENV=test jest --testPathPattern=e2e --detectOpenHandles --forceExit", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:ci": "cross-env NODE_ENV=test jest --coverage --ci --watchAll=false --detectOpenHandles --forceExit", "lint": "eslint . --ext .js,.ts --ignore-path .gitignore", "lint:fix": "eslint . --ext .js,.ts --fix --ignore-path .gitignore", "format": "prettier --write \"**/*.{js,ts,json,md}\" --ignore-path .gitignore", "format:check": "prettier --check \"**/*.{js,ts,json,md}\" --ignore-path .gitignore", "clean": "rimraf coverage dist build logs/*.log", "build": "echo 'Build completed - using Node.js runtime'", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f", "docker:restart": "npm run docker:down && npm run docker:up", "migrate": "cross-env NODE_ENV=development node scripts/migrate.js", "seed": "cross-env NODE_ENV=development node scripts/seed.js || echo 'Seed script not found, skipping...'", "benchmark": "cross-env NODE_ENV=development node scripts/benchmark.js", "docs:generate": "node scripts/generate-docs.js || echo 'Docs generation script not found'", "docs:serve": "swagger-ui-cli -f docs/swagger.json -p 8080 || echo 'Swagger docs not found'", "security:audit": "npm audit --audit-level=moderate", "security:fix": "npm audit fix", "security:snyk": "snyk test || echo 'Snyk not configured'", "performance:test": "artillery run tests/performance/load-test.yml || echo 'Performance tests not found'", "health:check": "node scripts/health-check.js", "logs:tail": "node scripts/tail-logs.js || echo 'Log files not found'", "logs:clean": "rimraf logs/*.log", "db:backup": "node scripts/backup-db.js || echo 'Backup script not found'", "db:restore": "node scripts/restore-db.js || echo 'Restore script not found'", "setup": "npm install && npm run prepare && npm run migrate", "prepare": "husky install || echo 'Husky setup skipped'", "postinstall": "npm run prepare", "precommit": "lint-staged", "prepush": "npm run test:ci", "validate": "npm run lint && npm run format:check && npm run test:ci", "deploy:staging": "npm run validate && npm run docker:build && kubectl apply -f infrastructure/k8s/staging/ || echo 'Staging deployment failed'", "deploy:production": "npm run validate && npm run docker:build && kubectl apply -f infrastructure/k8s/production/ || echo 'Production deployment failed'"}, "keywords": ["gps", "tracking", "ai", "microservices", "enterprise", "real-time", "geospatial", "mongodb", "nodejs", "production"], "author": "Enterprise GPS Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "ioredis": "^5.3.2", "bullmq": "^4.15.4", "socket.io": "^4.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-oauth2": "^1.7.0", "passport-google-oauth20": "^2.0.0", "passport-microsoft": "^1.0.0", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "winston-elasticsearch": "^0.17.4", "dotenv": "^16.3.1", "config": "^3.3.9", "dayjs": "^1.11.10", "lodash": "^4.17.21", "uuid": "^9.0.1", "nanoid": "^5.0.4", "joi": "^17.11.0", "ajv": "^8.12.0", "geolib": "^3.3.4", "@turf/turf": "^6.5.0", "ml-matrix": "^6.10.7", "ml-regression": "^6.0.1", "simple-statistics": "^7.8.3", "@tensorflow/tfjs": "^4.15.0", "@tensorflow/tfjs-node": "^4.15.0", "brain.js": "^2.0.0-beta.23", "node-cron": "^3.0.3", "agenda": "^5.0.0", "nodemailer": "^6.9.7", "twilio": "^4.19.3", "@aws-sdk/client-s3": "^3.478.0", "@aws-sdk/s3-request-presigner": "^3.478.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "sharp": "^0.33.1", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "pdfkit": "^0.14.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-openapi-validator": "^5.1.2", "http-proxy-middleware": "^2.0.6", "opossum": "^8.0.0", "prom-client": "^15.1.0", "jaeger-client": "^3.19.0", "newrelic": "^11.7.0", "dd-trace": "^4.22.0", "elastic-apm-node": "^4.2.0", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "handlebars": "^4.7.8", "validator": "^13.11.0", "cross-env": "^7.0.3", "rimraf": "^5.0.5", "chalk": "^4.1.2", "ora": "^5.4.1", "inquirer": "^8.2.6", "yargs": "^17.7.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "semver": "^7.5.4"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2", "jest": "^29.7.0", "supertest": "^6.3.3", "mongodb-memory-server": "^9.1.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-security": "^1.7.1", "eslint-plugin-node": "^11.1.0", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "snyk": "^1.1266.0", "artillery": "^2.0.3", "@types/node": "^20.10.5", "typescript": "^5.3.3", "ts-node": "^10.9.2", "@types/express": "^4.17.21", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/joi": "^17.2.3", "@types/multer": "^1.4.11", "@types/sharp": "^0.32.0", "@types/pdfkit": "^0.12.12", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/validator": "^13.11.7", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "jest-junit": "^16.0.0", "babel-jest": "^29.7.0", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "ioredis-mock": "^8.9.0", "wait-on": "^7.2.0", "kill-port": "^2.0.1", "npm-run-all": "^4.1.5", "swagger-ui-cli": "^4.15.5", "core-js": "^3.34.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/company/enterprise-ai-gps-tracker.git"}, "bugs": {"url": "https://github.com/company/enterprise-ai-gps-tracker/issues"}, "homepage": "https://github.com/company/enterprise-ai-gps-tracker#readme", "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js", "services/**/*.js", "!**/node_modules/**", "!**/coverage/**"], "testMatch": ["**/tests/**/*.test.js", "**/tests/**/*.spec.js"]}}