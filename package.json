{"name": "ai-gps-tracker", "version": "1.0.0", "description": "Advanced AI-powered GPS tracking system with real-time monitoring, geofencing, and intelligent analytics", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "seed": "node src/scripts/seedDatabase.js"}, "keywords": ["gps", "tracking", "ai", "geofencing", "real-time", "mongodb", "express", "nodejs"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "moment": "^2.29.4", "geolib": "^3.3.4", "ml-matrix": "^6.10.7", "simple-statistics": "^7.8.3", "node-cron": "^3.0.3", "winston": "^3.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0"}, "engines": {"node": ">=16.0.0"}}