/**
 * Main Application Entry Point
 */
class App {
    constructor() {
        this.initialized = false;
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                this.start();
            }
        } catch (error) {
            console.error('Failed to initialize app:', error);
        }
    }

    /**
     * Start the application
     */
    async start() {
        if (this.initialized) return;

        console.log('🚀 Starting AI GPS Tracker Dashboard...');

        try {
            // Initialize global error handling
            this.setupErrorHandling();

            // Initialize service worker (if available)
            this.initializeServiceWorker();

            // The auth manager will handle initial authentication
            // and show either login modal or dashboard
            
            this.initialized = true;
            console.log('✅ Application initialized successfully');

        } catch (error) {
            console.error('❌ Failed to start application:', error);
            this.showErrorMessage('Failed to start application. Please refresh the page.');
        }
    }

    /**
     * Setup global error handling
     */
    setupErrorHandling() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            
            // Don't show error for network issues during development
            if (event.reason?.message?.includes('fetch')) {
                return;
            }
            
            this.showErrorMessage('An unexpected error occurred. Please try again.');
        });

        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            console.error('JavaScript error:', event.error);
            
            // Don't show error for script loading issues
            if (event.filename && event.filename.includes('.js')) {
                return;
            }
            
            this.showErrorMessage('An unexpected error occurred. Please refresh the page.');
        });

        // Handle API errors globally
        window.addEventListener('apiError', (event) => {
            const { error, endpoint } = event.detail;
            console.error(`API Error on ${endpoint}:`, error);
            
            if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
                // Token expired or invalid
                if (window.auth) {
                    window.auth.logout();
                }
            } else {
                this.showErrorMessage(error.message || 'API request failed');
            }
        });
    }

    /**
     * Initialize service worker for offline support
     */
    async initializeServiceWorker() {
        if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered:', registration);
            } catch (error) {
                console.log('Service Worker registration failed:', error);
            }
        }
    }

    /**
     * Show error message to user
     * @param {string} message - Error message
     */
    showErrorMessage(message) {
        // Create error notification
        const errorEl = document.createElement('div');
        errorEl.className = 'error-notification';
        errorEl.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Style the error notification
        Object.assign(errorEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            backgroundColor: '#e74c3c',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '5px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: '10000',
            maxWidth: '400px',
            fontSize: '14px'
        });

        errorEl.querySelector('.error-content').style.display = 'flex';
        errorEl.querySelector('.error-content').style.alignItems = 'center';
        errorEl.querySelector('.error-content').style.gap = '10px';

        const closeBtn = errorEl.querySelector('.error-close');
        Object.assign(closeBtn.style, {
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            padding: '0',
            marginLeft: 'auto'
        });

        document.body.appendChild(errorEl);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (errorEl.parentNode) {
                errorEl.parentNode.removeChild(errorEl);
            }
        }, 10000);
    }

    /**
     * Get application info
     * @returns {Object} App info
     */
    getInfo() {
        return {
            name: 'AI GPS Tracker Dashboard',
            version: '1.0.0',
            initialized: this.initialized,
            timestamp: new Date().toISOString()
        };
    }
}

// Placeholder classes for features not yet implemented
class MapManager {
    initialize() {
        console.log('Map manager initialized (placeholder)');
        
        // Initialize Leaflet map
        if (document.getElementById('map')) {
            const map = L.map('map').setView([40.7128, -74.0060], 10); // Default to NYC
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            this.map = map;
            this.markers = new Map();
        }
    }

    updateDeviceLocation(data) {
        console.log('Updating device location on map:', data);
        // Implementation would update marker position
    }
}

class Analytics {
    loadCharts() {
        console.log('Loading analytics charts (placeholder)');
        
        // Initialize placeholder charts
        this.initializePlaceholderCharts();
    }

    initializePlaceholderCharts() {
        const chartIds = ['distance-chart', 'speed-chart', 'activity-chart', 'battery-chart'];
        
        chartIds.forEach(chartId => {
            const canvas = document.getElementById(chartId);
            if (canvas) {
                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        datasets: [{
                            label: 'Sample Data',
                            data: [12, 19, 3, 5, 2, 3, 7],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        });
    }
}

class AIManager {
    initialize() {
        console.log('AI manager initialized (placeholder)');
        
        // Populate device selector
        this.populateDeviceSelector();
        this.bindEvents();
    }

    populateDeviceSelector() {
        const selector = document.getElementById('ai-device-selector');
        if (selector && window.dashboard?.devices) {
            selector.innerHTML = '<option value="">Select a device...</option>' +
                window.dashboard.devices.map(device => 
                    `<option value="${device._id}">${device.name}</option>`
                ).join('');
        }
    }

    bindEvents() {
        const selector = document.getElementById('ai-device-selector');
        if (selector) {
            selector.addEventListener('change', (e) => {
                if (e.target.value) {
                    this.loadAIInsights(e.target.value);
                }
            });
        }
    }

    async loadAIInsights(deviceId) {
        console.log('Loading AI insights for device:', deviceId);
        
        // Show loading state
        const aiCards = document.querySelectorAll('.ai-content');
        aiCards.forEach(card => {
            card.innerHTML = '<p class="loading">Loading AI insights...</p>';
        });

        try {
            // Load route prediction
            const prediction = await api.getRoutePrediction(deviceId);
            this.displayRoutePrediction(prediction);

            // Load device insights
            const insights = await api.getDeviceInsights(deviceId);
            this.displayInsights(insights);

            // Load movement patterns
            const patterns = await api.getMovementPatterns(deviceId);
            this.displayMovementPatterns(patterns);

        } catch (error) {
            console.error('Failed to load AI insights:', error);
            aiCards.forEach(card => {
                card.innerHTML = '<p class="error">Failed to load AI insights</p>';
            });
        }
    }

    displayRoutePrediction(data) {
        const container = document.getElementById('route-prediction');
        if (container && data.success && data.data.prediction) {
            const pred = data.data.prediction;
            container.innerHTML = `
                <div class="prediction-item">
                    <strong>Predicted Location:</strong><br>
                    Lat: ${pred.latitude.toFixed(6)}, Lng: ${pred.longitude.toFixed(6)}
                </div>
                <div class="prediction-item">
                    <strong>Estimated Time:</strong><br>
                    ${new Date(pred.estimatedTime).toLocaleString()}
                </div>
                <div class="prediction-item">
                    <strong>Confidence:</strong><br>
                    ${(pred.confidence * 100).toFixed(1)}%
                </div>
            `;
        } else {
            container.innerHTML = '<p>No prediction data available</p>';
        }
    }

    displayInsights(data) {
        const container = document.getElementById('recommendations');
        if (container && data.success && data.data.insights) {
            const insights = data.data.insights;
            const recommendations = insights.recommendations || [];
            
            if (recommendations.length > 0) {
                container.innerHTML = recommendations.map(rec => `
                    <div class="recommendation-item priority-${rec.priority}">
                        <i class="fas fa-lightbulb"></i>
                        <span>${rec.message}</span>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p>No recommendations available</p>';
            }
        }
    }

    displayMovementPatterns(data) {
        const container = document.getElementById('movement-patterns');
        if (container && data.success && data.data.patterns) {
            const patterns = data.data.patterns;
            container.innerHTML = `
                <div class="pattern-item">
                    <strong>Frequent Locations:</strong><br>
                    ${patterns.frequentLocations?.length || 0} locations identified
                </div>
                <div class="pattern-item">
                    <strong>Route Patterns:</strong><br>
                    ${patterns.routePatterns?.length || 0} routes analyzed
                </div>
                <div class="pattern-item">
                    <strong>Peak Hours:</strong><br>
                    ${patterns.timePatterns?.peakHours?.join(', ') || 'N/A'}
                </div>
            `;
        } else {
            container.innerHTML = '<p>No pattern data available</p>';
        }
    }
}

// Create global instances
window.mapManager = new MapManager();
window.analytics = new Analytics();
window.aiManager = new AIManager();

// Start the application
window.app = new App();

// Export for debugging
window.appInfo = () => window.app.getInfo();
