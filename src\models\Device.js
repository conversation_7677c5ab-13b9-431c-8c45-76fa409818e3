const mongoose = require('mongoose');

/**
 * Device Schema for GPS Tracking
 */
const deviceSchema = new mongoose.Schema({
  // Device Identification
  deviceId: {
    type: String,
    required: [true, 'Device ID is required'],
    unique: true,
    trim: true,
    uppercase: true,
    match: [/^[A-Z0-9]{8,20}$/, 'Device ID must be 8-20 alphanumeric characters']
  },
  
  name: {
    type: String,
    required: [true, 'Device name is required'],
    trim: true,
    maxlength: [100, 'Device name cannot exceed 100 characters']
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  // Owner Information
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Device owner is required']
  },
  
  // Device Specifications
  type: {
    type: String,
    enum: ['vehicle', 'person', 'asset', 'pet', 'other'],
    required: [true, 'Device type is required']
  },
  
  model: {
    type: String,
    trim: true,
    maxlength: [100, 'Model cannot exceed 100 characters']
  },
  
  manufacturer: {
    type: String,
    trim: true,
    maxlength: [100, 'Manufacturer cannot exceed 100 characters']
  },
  
  serialNumber: {
    type: String,
    trim: true,
    maxlength: [100, 'Serial number cannot exceed 100 characters']
  },
  
  // Status and Configuration
  isActive: {
    type: Boolean,
    default: true
  },
  
  isOnline: {
    type: Boolean,
    default: false
  },
  
  batteryLevel: {
    type: Number,
    min: [0, 'Battery level cannot be negative'],
    max: [100, 'Battery level cannot exceed 100'],
    default: null
  },
  
  signalStrength: {
    type: Number,
    min: [-120, 'Signal strength too low'],
    max: [0, 'Signal strength too high'],
    default: null
  },
  
  // Location Settings
  trackingInterval: {
    type: Number,
    min: [1, 'Tracking interval must be at least 1 second'],
    max: [3600, 'Tracking interval cannot exceed 1 hour'],
    default: 30 // seconds
  },
  
  accuracy: {
    type: String,
    enum: ['high', 'medium', 'low'],
    default: 'medium'
  },
  
  // Last Known Information
  lastLocation: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates format'
      }
    },
    address: String,
    timestamp: Date
  },
  
  lastSeen: {
    type: Date,
    default: null
  },
  
  // Communication Settings
  phoneNumber: {
    type: String,
    trim: true,
    match: [/^\+?[\d\s-()]+$/, 'Please enter a valid phone number']
  },
  
  simCardNumber: {
    type: String,
    trim: true
  },
  
  // Alerts and Notifications
  alerts: {
    lowBattery: {
      enabled: { type: Boolean, default: true },
      threshold: { type: Number, min: 0, max: 100, default: 20 }
    },
    offline: {
      enabled: { type: Boolean, default: true },
      timeout: { type: Number, min: 60, default: 300 } // seconds
    },
    geofence: {
      enabled: { type: Boolean, default: true }
    },
    speed: {
      enabled: { type: Boolean, default: false },
      limit: { type: Number, min: 0, default: 80 } // km/h
    }
  },
  
  // Maintenance and Warranty
  purchaseDate: {
    type: Date,
    default: null
  },
  
  warrantyExpiry: {
    type: Date,
    default: null
  },
  
  lastMaintenance: {
    type: Date,
    default: null
  },
  
  // Statistics
  stats: {
    totalDistance: { type: Number, default: 0 }, // in kilometers
    totalRuntime: { type: Number, default: 0 }, // in hours
    averageSpeed: { type: Number, default: 0 }, // in km/h
    maxSpeed: { type: Number, default: 0 }, // in km/h
    lastUpdated: { type: Date, default: Date.now }
  },
  
  // Custom Fields
  customFields: {
    type: Map,
    of: String,
    default: new Map()
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
deviceSchema.index({ deviceId: 1 });
deviceSchema.index({ owner: 1 });
deviceSchema.index({ isActive: 1 });
deviceSchema.index({ isOnline: 1 });
deviceSchema.index({ type: 1 });
deviceSchema.index({ lastSeen: -1 });
deviceSchema.index({ 'lastLocation.coordinates': '2dsphere' });

// Virtual for device status
deviceSchema.virtual('status').get(function() {
  if (!this.isActive) return 'inactive';
  if (!this.isOnline) return 'offline';
  if (this.batteryLevel !== null && this.batteryLevel < this.alerts.lowBattery.threshold) {
    return 'low_battery';
  }
  return 'online';
});

// Virtual for battery status
deviceSchema.virtual('batteryStatus').get(function() {
  if (this.batteryLevel === null) return 'unknown';
  if (this.batteryLevel >= 80) return 'good';
  if (this.batteryLevel >= 50) return 'medium';
  if (this.batteryLevel >= 20) return 'low';
  return 'critical';
});

// Virtual for connection status
deviceSchema.virtual('connectionStatus').get(function() {
  if (!this.lastSeen) return 'never_connected';
  
  const now = new Date();
  const timeDiff = (now - this.lastSeen) / 1000; // seconds
  
  if (timeDiff <= this.trackingInterval * 2) return 'connected';
  if (timeDiff <= this.alerts.offline.timeout) return 'delayed';
  return 'disconnected';
});

// Instance method to update location
deviceSchema.methods.updateLocation = function(longitude, latitude, address = null) {
  this.lastLocation = {
    type: 'Point',
    coordinates: [longitude, latitude],
    address,
    timestamp: new Date()
  };
  this.lastSeen = new Date();
  this.isOnline = true;
  
  return this.save();
};

// Instance method to update battery level
deviceSchema.methods.updateBattery = function(level) {
  if (level < 0 || level > 100) {
    throw new Error('Battery level must be between 0 and 100');
  }
  
  this.batteryLevel = level;
  this.lastSeen = new Date();
  
  return this.save();
};

// Static method to find devices by owner
deviceSchema.statics.findByOwner = function(ownerId, options = {}) {
  const query = { owner: ownerId };
  
  if (options.active !== undefined) {
    query.isActive = options.active;
  }
  
  if (options.online !== undefined) {
    query.isOnline = options.online;
  }
  
  if (options.type) {
    query.type = options.type;
  }
  
  return this.find(query).populate('owner', 'username email firstName lastName');
};

// Static method to find nearby devices
deviceSchema.statics.findNearby = function(longitude, latitude, maxDistance = 1000) {
  return this.find({
    'lastLocation.coordinates': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    },
    isActive: true
  });
};

module.exports = mongoose.model('Device', deviceSchema);
