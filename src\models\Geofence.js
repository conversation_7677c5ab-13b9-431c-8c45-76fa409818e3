const mongoose = require('mongoose');

/**
 * Geofence Schema for Location-based Alerts
 */
const geofenceSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: [true, 'Geofence name is required'],
    trim: true,
    maxlength: [100, 'Geofence name cannot exceed 100 characters']
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  // Owner Information
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Geofence owner is required'],
    index: true
  },
  
  // Associated Devices
  devices: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device'
  }],
  
  // Geofence Type and Shape
  type: {
    type: String,
    enum: ['circle', 'polygon', 'rectangle'],
    required: [true, 'Geofence type is required']
  },
  
  // Geographic Definition
  geometry: {
    // For circle type
    center: {
      type: {
        type: String,
        enum: ['Point']
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        validate: {
          validator: function(coords) {
            return !coords || (coords.length === 2 && 
                   coords[0] >= -180 && coords[0] <= 180 && // longitude
                   coords[1] >= -90 && coords[1] <= 90);     // latitude
          },
          message: 'Invalid center coordinates'
        }
      }
    },
    
    radius: {
      type: Number,
      min: [1, 'Radius must be at least 1 meter'],
      max: [100000, 'Radius cannot exceed 100km']
    },
    
    // For polygon/rectangle type
    polygon: {
      type: {
        type: String,
        enum: ['Polygon']
      },
      coordinates: {
        type: [[[Number]]], // Array of linear rings
        validate: {
          validator: function(coords) {
            if (!coords || coords.length === 0) return true;
            
            // Check if it's a valid polygon structure
            const ring = coords[0];
            if (!ring || ring.length < 4) return false;
            
            // Check if first and last points are the same (closed ring)
            const first = ring[0];
            const last = ring[ring.length - 1];
            return first[0] === last[0] && first[1] === last[1];
          },
          message: 'Invalid polygon coordinates'
        }
      }
    }
  },
  
  // Alert Configuration
  alerts: {
    onEnter: {
      enabled: { type: Boolean, default: true },
      message: { type: String, default: 'Device entered geofence' },
      recipients: [String] // email addresses or phone numbers
    },
    
    onExit: {
      enabled: { type: Boolean, default: true },
      message: { type: String, default: 'Device exited geofence' },
      recipients: [String]
    },
    
    onDwell: {
      enabled: { type: Boolean, default: false },
      duration: { type: Number, min: 60, default: 300 }, // seconds
      message: { type: String, default: 'Device dwelling in geofence' },
      recipients: [String]
    }
  },
  
  // Schedule and Conditions
  schedule: {
    enabled: { type: Boolean, default: false },
    timezone: { type: String, default: 'UTC' },
    days: [{
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    }],
    timeRanges: [{
      start: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ }, // HH:MM format
      end: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ }
    }]
  },
  
  // Status and Configuration
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  
  color: {
    type: String,
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
    default: '#3498db'
  },
  
  // Statistics
  stats: {
    totalEntries: { type: Number, default: 0 },
    totalExits: { type: Number, default: 0 },
    lastTriggered: { type: Date, default: null },
    averageDwellTime: { type: Number, default: 0 } // in seconds
  },
  
  // Metadata
  tags: [String],
  
  customFields: {
    type: Map,
    of: String,
    default: new Map()
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
geofenceSchema.index({ owner: 1 });
geofenceSchema.index({ isActive: 1 });
geofenceSchema.index({ devices: 1 });
geofenceSchema.index({ 'geometry.center': '2dsphere' });
geofenceSchema.index({ 'geometry.polygon': '2dsphere' });
geofenceSchema.index({ priority: 1 });
geofenceSchema.index({ tags: 1 });

// Virtual for area calculation
geofenceSchema.virtual('area').get(function() {
  if (this.type === 'circle') {
    return Math.PI * Math.pow(this.geometry.radius, 2);
  }
  
  if (this.type === 'polygon' && this.geometry.polygon) {
    // Simplified area calculation for polygon
    // In a real application, you might want to use a more sophisticated algorithm
    return this.calculatePolygonArea(this.geometry.polygon.coordinates[0]);
  }
  
  return 0;
});

// Virtual for center coordinates (works for all types)
geofenceSchema.virtual('centerCoordinates').get(function() {
  if (this.type === 'circle') {
    return this.geometry.center.coordinates;
  }
  
  if (this.type === 'polygon' && this.geometry.polygon) {
    return this.calculatePolygonCenter(this.geometry.polygon.coordinates[0]);
  }
  
  return [0, 0];
});

// Instance method to check if a point is inside the geofence
geofenceSchema.methods.containsPoint = function(longitude, latitude) {
  if (this.type === 'circle') {
    return this.isPointInCircle(longitude, latitude);
  }
  
  if (this.type === 'polygon') {
    return this.isPointInPolygon(longitude, latitude);
  }
  
  return false;
};

// Helper method to check if point is in circle
geofenceSchema.methods.isPointInCircle = function(longitude, latitude) {
  if (!this.geometry.center || !this.geometry.radius) return false;
  
  const centerLon = this.geometry.center.coordinates[0];
  const centerLat = this.geometry.center.coordinates[1];
  
  const distance = this.calculateDistance(centerLat, centerLon, latitude, longitude);
  return distance <= this.geometry.radius;
};

// Helper method to check if point is in polygon (ray casting algorithm)
geofenceSchema.methods.isPointInPolygon = function(longitude, latitude) {
  if (!this.geometry.polygon || !this.geometry.polygon.coordinates[0]) return false;
  
  const polygon = this.geometry.polygon.coordinates[0];
  let inside = false;
  
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i][0], yi = polygon[i][1];
    const xj = polygon[j][0], yj = polygon[j][1];
    
    if (((yi > latitude) !== (yj > latitude)) &&
        (longitude < (xj - xi) * (latitude - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }
  
  return inside;
};

// Helper method to calculate distance between two points
geofenceSchema.methods.calculateDistance = function(lat1, lon1, lat2, lon2) {
  const R = 6371000; // Earth's radius in meters
  const dLat = this.toRadians(lat2 - lat1);
  const dLon = this.toRadians(lon2 - lon1);
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

// Helper method to convert degrees to radians
geofenceSchema.methods.toRadians = function(degrees) {
  return degrees * (Math.PI / 180);
};

// Helper method to calculate polygon area (Shoelace formula)
geofenceSchema.methods.calculatePolygonArea = function(coordinates) {
  let area = 0;
  const n = coordinates.length;
  
  for (let i = 0; i < n - 1; i++) {
    area += coordinates[i][0] * coordinates[i + 1][1];
    area -= coordinates[i + 1][0] * coordinates[i][1];
  }
  
  return Math.abs(area) / 2;
};

// Helper method to calculate polygon center (centroid)
geofenceSchema.methods.calculatePolygonCenter = function(coordinates) {
  let x = 0, y = 0;
  const n = coordinates.length - 1; // Exclude the last point (same as first)
  
  for (let i = 0; i < n; i++) {
    x += coordinates[i][0];
    y += coordinates[i][1];
  }
  
  return [x / n, y / n];
};

// Instance method to check if geofence is active at current time
geofenceSchema.methods.isActiveNow = function() {
  if (!this.isActive) return false;
  if (!this.schedule.enabled) return true;
  
  const now = new Date();
  const dayName = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
  
  // Check if current day is in schedule
  if (!this.schedule.days.includes(dayName)) return false;
  
  // Check if current time is in any time range
  return this.schedule.timeRanges.some(range => {
    return currentTime >= range.start && currentTime <= range.end;
  });
};

// Static method to find geofences containing a point
geofenceSchema.statics.findContaining = function(longitude, latitude, options = {}) {
  const query = { isActive: true };
  
  if (options.owner) {
    query.owner = options.owner;
  }
  
  if (options.devices && options.devices.length > 0) {
    query.devices = { $in: options.devices };
  }
  
  return this.find(query).then(geofences => {
    return geofences.filter(geofence => 
      geofence.containsPoint(longitude, latitude) && geofence.isActiveNow()
    );
  });
};

// Static method to find geofences by owner
geofenceSchema.statics.findByOwner = function(ownerId, options = {}) {
  const query = { owner: ownerId };
  
  if (options.active !== undefined) {
    query.isActive = options.active;
  }
  
  if (options.type) {
    query.type = options.type;
  }
  
  return this.find(query).populate('devices', 'name deviceId type');
};

module.exports = mongoose.model('Geofence', geofenceSchema);
