const express = require('express');
const DeviceController = require('../controllers/deviceController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Device:
 *       type: object
 *       required:
 *         - deviceId
 *         - name
 *         - type
 *       properties:
 *         deviceId:
 *           type: string
 *           description: Unique device identifier
 *         name:
 *           type: string
 *           description: Device name
 *         type:
 *           type: string
 *           enum: [vehicle, person, asset, pet, other]
 *           description: Device type
 *         description:
 *           type: string
 *           description: Device description
 *         model:
 *           type: string
 *           description: Device model
 *         manufacturer:
 *           type: string
 *           description: Device manufacturer
 *         serialNumber:
 *           type: string
 *           description: Device serial number
 *         isActive:
 *           type: boolean
 *           description: Device active status
 *         isOnline:
 *           type: boolean
 *           description: Device online status
 *         batteryLevel:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           description: Battery level percentage
 *         lastLocation:
 *           type: object
 *           properties:
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *               description: [longitude, latitude]
 *             timestamp:
 *               type: string
 *               format: date-time
 */

/**
 * @swagger
 * /api/devices:
 *   post:
 *     summary: Register a new device
 *     tags: [Devices]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Device'
 *     responses:
 *       201:
 *         description: Device registered successfully
 *       400:
 *         description: Validation error or device already exists
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateDeviceRegistration(),
  DeviceController.registerDevice
);

/**
 * @swagger
 * /api/devices:
 *   get:
 *     summary: Get all devices for the authenticated user
 *     tags: [Devices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of devices per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [vehicle, person, asset, pet, other]
 *         description: Filter by device type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, online, offline]
 *         description: Filter by device status
 *     responses:
 *       200:
 *         description: Devices retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     devices:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Device'
 *                     pagination:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validatePagination(),
  DeviceController.getDevices
);

/**
 * @swagger
 * /api/devices/{id}:
 *   get:
 *     summary: Get a specific device by ID
 *     tags: [Devices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     responses:
 *       200:
 *         description: Device retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     device:
 *                       $ref: '#/components/schemas/Device'
 *                     latestLocation:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  DeviceController.getDevice
);

/**
 * @swagger
 * /api/devices/{id}:
 *   put:
 *     summary: Update device information
 *     tags: [Devices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               model:
 *                 type: string
 *               manufacturer:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *               trackingInterval:
 *                 type: number
 *               alerts:
 *                 type: object
 *     responses:
 *       200:
 *         description: Device updated successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.put('/:id',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  DeviceController.updateDevice
);

/**
 * @swagger
 * /api/devices/{id}:
 *   delete:
 *     summary: Delete a device
 *     tags: [Devices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     responses:
 *       200:
 *         description: Device deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.delete('/:id',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  DeviceController.deleteDevice
);

/**
 * @swagger
 * /api/devices/{id}/stats:
 *   get:
 *     summary: Get device statistics
 *     tags: [Devices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 7
 *         description: Number of days for statistics
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id/stats',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  DeviceController.getDeviceStats
);

/**
 * @swagger
 * /api/devices/{id}/status:
 *   patch:
 *     summary: Update device status
 *     tags: [Devices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isOnline:
 *                 type: boolean
 *               batteryLevel:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *               signalStrength:
 *                 type: number
 *                 minimum: -120
 *                 maximum: 0
 *     responses:
 *       200:
 *         description: Device status updated successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.patch('/:id/status',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  DeviceController.updateDeviceStatus
);

module.exports = router;
