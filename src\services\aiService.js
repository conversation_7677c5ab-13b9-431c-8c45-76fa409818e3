const { Matrix } = require('ml-matrix');
const ss = require('simple-statistics');
const Location = require('../models/Location');
const Device = require('../models/Device');
const Alert = require('../models/Alert');
const logger = require('../utils/logger');

/**
 * AI Service for GPS Tracking Intelligence
 */
class AIService {
  constructor() {
    this.models = new Map();
    this.anomalyThreshold = parseFloat(process.env.ANOMALY_THRESHOLD) || 0.8;
    this.learningRate = parseFloat(process.env.LEARNING_RATE) || 0.01;
  }
  
  /**
   * Predict next location based on historical data
   * @param {string} deviceId - Device ID
   * @param {number} timeHorizon - Prediction time horizon in minutes
   * @returns {Object} Predicted location and confidence
   */
  async predictNextLocation(deviceId, timeHorizon = 30) {
    try {
      // Get recent location history
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000); // Last 24 hours
      
      const locations = await Location.find({
        device: deviceId,
        timestamp: { $gte: startTime, $lte: endTime }
      }).sort({ timestamp: 1 });
      
      if (locations.length < 5) {
        return {
          success: false,
          message: 'Insufficient data for prediction',
          minDataPoints: 5,
          currentDataPoints: locations.length
        };
      }
      
      // Extract features for prediction
      const features = this.extractLocationFeatures(locations);
      
      // Use linear regression for simple route prediction
      const prediction = this.performLinearRegression(features, timeHorizon);
      
      // Calculate confidence based on historical accuracy
      const confidence = this.calculatePredictionConfidence(locations, prediction);
      
      return {
        success: true,
        prediction: {
          latitude: prediction.latitude,
          longitude: prediction.longitude,
          estimatedTime: new Date(endTime.getTime() + timeHorizon * 60 * 1000),
          confidence: confidence,
          timeHorizon: timeHorizon
        },
        metadata: {
          dataPoints: locations.length,
          algorithm: 'linear_regression',
          features: Object.keys(features)
        }
      };
      
    } catch (error) {
      logger.error('Route prediction error:', error);
      return {
        success: false,
        message: 'Prediction failed',
        error: error.message
      };
    }
  }
  
  /**
   * Detect anomalies in location data
   * @param {string} deviceId - Device ID
   * @param {Object} currentLocation - Current location data
   * @returns {Object} Anomaly detection result
   */
  async detectAnomalies(deviceId, currentLocation) {
    try {
      // Get historical data for comparison
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000); // Last 7 days
      
      const historicalLocations = await Location.find({
        device: deviceId,
        timestamp: { $gte: startTime, $lte: endTime }
      }).sort({ timestamp: -1 });
      
      if (historicalLocations.length < 10) {
        return {
          isAnomaly: false,
          confidence: 0,
          message: 'Insufficient historical data for anomaly detection'
        };
      }
      
      const anomalies = [];
      let totalAnomalyScore = 0;
      
      // Speed anomaly detection
      const speedAnomaly = this.detectSpeedAnomaly(currentLocation, historicalLocations);
      if (speedAnomaly.isAnomaly) {
        anomalies.push(speedAnomaly);
        totalAnomalyScore += speedAnomaly.score;
      }
      
      // Location anomaly detection (unusual location)
      const locationAnomaly = this.detectLocationAnomaly(currentLocation, historicalLocations);
      if (locationAnomaly.isAnomaly) {
        anomalies.push(locationAnomaly);
        totalAnomalyScore += locationAnomaly.score;
      }
      
      // Time-based anomaly detection (unusual time patterns)
      const timeAnomaly = this.detectTimeAnomaly(currentLocation, historicalLocations);
      if (timeAnomaly.isAnomaly) {
        anomalies.push(timeAnomaly);
        totalAnomalyScore += timeAnomaly.score;
      }
      
      // Route deviation detection
      const routeAnomaly = this.detectRouteDeviation(currentLocation, historicalLocations);
      if (routeAnomaly.isAnomaly) {
        anomalies.push(routeAnomaly);
        totalAnomalyScore += routeAnomaly.score;
      }
      
      const averageScore = anomalies.length > 0 ? totalAnomalyScore / anomalies.length : 0;
      const isAnomaly = averageScore > this.anomalyThreshold;
      
      // Create alert if anomaly detected
      if (isAnomaly) {
        await this.createAnomalyAlert(deviceId, currentLocation, anomalies, averageScore);
      }
      
      return {
        isAnomaly,
        confidence: averageScore,
        anomalies,
        threshold: this.anomalyThreshold,
        metadata: {
          historicalDataPoints: historicalLocations.length,
          detectionMethods: ['speed', 'location', 'time', 'route']
        }
      };
      
    } catch (error) {
      logger.error('Anomaly detection error:', error);
      return {
        isAnomaly: false,
        confidence: 0,
        error: error.message
      };
    }
  }
  
  /**
   * Extract features from location data for ML
   * @param {Array} locations - Array of location objects
   * @returns {Object} Extracted features
   */
  extractLocationFeatures(locations) {
    const features = {
      coordinates: [],
      speeds: [],
      headings: [],
      timestamps: [],
      timeDeltas: [],
      distances: []
    };
    
    for (let i = 0; i < locations.length; i++) {
      const loc = locations[i];
      
      features.coordinates.push([
        loc.location.coordinates[0], // longitude
        loc.location.coordinates[1]  // latitude
      ]);
      
      features.speeds.push(loc.speed || 0);
      features.headings.push(loc.heading || 0);
      features.timestamps.push(loc.timestamp.getTime());
      
      if (i > 0) {
        const prevLoc = locations[i - 1];
        const timeDelta = (loc.timestamp - prevLoc.timestamp) / 1000; // seconds
        features.timeDeltas.push(timeDelta);
        features.distances.push(loc.movement?.distance || 0);
      }
    }
    
    return features;
  }
  
  /**
   * Perform linear regression for route prediction
   * @param {Object} features - Extracted features
   * @param {number} timeHorizon - Time horizon in minutes
   * @returns {Object} Predicted coordinates
   */
  performLinearRegression(features, timeHorizon) {
    const coordinates = features.coordinates;
    const timestamps = features.timestamps;
    
    if (coordinates.length < 2) {
      return { latitude: 0, longitude: 0 };
    }
    
    // Simple linear regression on coordinates vs time
    const timePoints = timestamps.map((t, i) => i);
    const latitudes = coordinates.map(coord => coord[1]);
    const longitudes = coordinates.map(coord => coord[0]);
    
    // Calculate linear regression for latitude
    const latRegression = ss.linearRegression(timePoints, latitudes);
    
    // Calculate linear regression for longitude
    const lonRegression = ss.linearRegression(timePoints, longitudes);
    
    // Predict future point
    const futureTimePoint = timePoints.length + (timeHorizon / 5); // Assuming 5-minute intervals
    
    const predictedLat = latRegression.m * futureTimePoint + latRegression.b;
    const predictedLon = lonRegression.m * futureTimePoint + lonRegression.b;
    
    return {
      latitude: predictedLat,
      longitude: predictedLon
    };
  }
  
  /**
   * Calculate prediction confidence
   * @param {Array} locations - Historical locations
   * @param {Object} prediction - Predicted location
   * @returns {number} Confidence score (0-1)
   */
  calculatePredictionConfidence(locations, prediction) {
    // Simple confidence calculation based on data consistency
    const speeds = locations.map(loc => loc.speed || 0).filter(speed => speed > 0);
    const headings = locations.map(loc => loc.heading || 0).filter(heading => heading > 0);
    
    let confidence = 0.5; // Base confidence
    
    // Increase confidence if we have consistent speed data
    if (speeds.length > 0) {
      const speedVariance = ss.variance(speeds);
      confidence += Math.max(0, 0.3 - speedVariance / 1000);
    }
    
    // Increase confidence if we have consistent heading data
    if (headings.length > 0) {
      const headingVariance = ss.variance(headings);
      confidence += Math.max(0, 0.2 - headingVariance / 10000);
    }
    
    return Math.min(1, Math.max(0, confidence));
  }
  
  /**
   * Detect speed anomalies
   * @param {Object} currentLocation - Current location
   * @param {Array} historicalLocations - Historical locations
   * @returns {Object} Speed anomaly result
   */
  detectSpeedAnomaly(currentLocation, historicalLocations) {
    const currentSpeed = currentLocation.speed || 0;
    const historicalSpeeds = historicalLocations
      .map(loc => loc.speed || 0)
      .filter(speed => speed > 0);
    
    if (historicalSpeeds.length < 5) {
      return { isAnomaly: false, score: 0, type: 'speed' };
    }
    
    const meanSpeed = ss.mean(historicalSpeeds);
    const stdSpeed = ss.standardDeviation(historicalSpeeds);
    
    // Z-score calculation
    const zScore = Math.abs((currentSpeed - meanSpeed) / stdSpeed);
    
    // Consider it anomalous if z-score > 2 (95% confidence)
    const isAnomaly = zScore > 2;
    const score = Math.min(1, zScore / 3); // Normalize to 0-1
    
    return {
      isAnomaly,
      score,
      type: 'speed',
      details: {
        currentSpeed,
        meanSpeed: meanSpeed.toFixed(2),
        standardDeviation: stdSpeed.toFixed(2),
        zScore: zScore.toFixed(2)
      }
    };
  }
  
  /**
   * Detect location anomalies
   * @param {Object} currentLocation - Current location
   * @param {Array} historicalLocations - Historical locations
   * @returns {Object} Location anomaly result
   */
  detectLocationAnomaly(currentLocation, historicalLocations) {
    const currentCoords = [currentLocation.longitude, currentLocation.latitude];
    
    // Calculate distances to all historical locations
    const distances = historicalLocations.map(loc => {
      const historicalCoords = [
        loc.location.coordinates[0],
        loc.location.coordinates[1]
      ];
      return this.calculateDistance(currentCoords, historicalCoords);
    });
    
    if (distances.length < 5) {
      return { isAnomaly: false, score: 0, type: 'location' };
    }
    
    const meanDistance = ss.mean(distances);
    const stdDistance = ss.standardDeviation(distances);
    const minDistance = Math.min(...distances);
    
    // If current location is very far from any historical location
    const isAnomaly = minDistance > meanDistance + 2 * stdDistance;
    const score = Math.min(1, minDistance / (meanDistance + 2 * stdDistance));
    
    return {
      isAnomaly,
      score,
      type: 'location',
      details: {
        minDistanceToHistorical: minDistance.toFixed(2),
        meanDistance: meanDistance.toFixed(2),
        standardDeviation: stdDistance.toFixed(2)
      }
    };
  }
  
  /**
   * Detect time-based anomalies
   * @param {Object} currentLocation - Current location
   * @param {Array} historicalLocations - Historical locations
   * @returns {Object} Time anomaly result
   */
  detectTimeAnomaly(currentLocation, historicalLocations) {
    const currentHour = new Date(currentLocation.timestamp).getHours();
    const currentDay = new Date(currentLocation.timestamp).getDay();
    
    // Analyze historical time patterns
    const hourCounts = new Array(24).fill(0);
    const dayCounts = new Array(7).fill(0);
    
    historicalLocations.forEach(loc => {
      const hour = new Date(loc.timestamp).getHours();
      const day = new Date(loc.timestamp).getDay();
      hourCounts[hour]++;
      dayCounts[day]++;
    });
    
    // Calculate probability of current time
    const totalLocations = historicalLocations.length;
    const hourProbability = hourCounts[currentHour] / totalLocations;
    const dayProbability = dayCounts[currentDay] / totalLocations;
    
    // Low probability indicates anomaly
    const combinedProbability = hourProbability * dayProbability;
    const isAnomaly = combinedProbability < 0.05; // Less than 5% probability
    const score = Math.max(0, 1 - combinedProbability * 20);
    
    return {
      isAnomaly,
      score,
      type: 'time',
      details: {
        currentHour,
        currentDay,
        hourProbability: hourProbability.toFixed(3),
        dayProbability: dayProbability.toFixed(3),
        combinedProbability: combinedProbability.toFixed(3)
      }
    };
  }
  
  /**
   * Detect route deviation
   * @param {Object} currentLocation - Current location
   * @param {Array} historicalLocations - Historical locations
   * @returns {Object} Route deviation result
   */
  detectRouteDeviation(currentLocation, historicalLocations) {
    // This is a simplified route deviation detection
    // In a real implementation, you might use more sophisticated algorithms
    
    const recentLocations = historicalLocations.slice(0, 10); // Last 10 locations
    
    if (recentLocations.length < 3) {
      return { isAnomaly: false, score: 0, type: 'route' };
    }
    
    // Calculate expected direction based on recent movement
    const directions = [];
    for (let i = 1; i < recentLocations.length; i++) {
      const prev = recentLocations[i];
      const curr = recentLocations[i - 1];
      
      const deltaLat = curr.location.coordinates[1] - prev.location.coordinates[1];
      const deltaLon = curr.location.coordinates[0] - prev.location.coordinates[0];
      
      const direction = Math.atan2(deltaLat, deltaLon) * 180 / Math.PI;
      directions.push(direction);
    }
    
    if (directions.length === 0) {
      return { isAnomaly: false, score: 0, type: 'route' };
    }
    
    const meanDirection = ss.mean(directions);
    const currentDirection = currentLocation.heading || 0;
    
    // Calculate angular difference
    let angleDiff = Math.abs(currentDirection - meanDirection);
    if (angleDiff > 180) {
      angleDiff = 360 - angleDiff;
    }
    
    // Consider significant deviation if > 90 degrees
    const isAnomaly = angleDiff > 90;
    const score = Math.min(1, angleDiff / 180);
    
    return {
      isAnomaly,
      score,
      type: 'route',
      details: {
        currentHeading: currentDirection,
        expectedDirection: meanDirection.toFixed(2),
        angleDifference: angleDiff.toFixed(2)
      }
    };
  }
  
  /**
   * Calculate distance between two coordinates
   * @param {Array} coord1 - [longitude, latitude]
   * @param {Array} coord2 - [longitude, latitude]
   * @returns {number} Distance in meters
   */
  calculateDistance(coord1, coord2) {
    const R = 6371000; // Earth's radius in meters
    const lat1 = coord1[1] * Math.PI / 180;
    const lat2 = coord2[1] * Math.PI / 180;
    const deltaLat = (coord2[1] - coord1[1]) * Math.PI / 180;
    const deltaLon = (coord2[0] - coord1[0]) * Math.PI / 180;
    
    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c;
  }
  
  /**
   * Create anomaly alert
   * @param {string} deviceId - Device ID
   * @param {Object} location - Current location
   * @param {Array} anomalies - Detected anomalies
   * @param {number} score - Anomaly score
   */
  async createAnomalyAlert(deviceId, location, anomalies, score) {
    try {
      const device = await Device.findById(deviceId);
      if (!device) return;
      
      const alertData = {
        type: 'anomaly_detected',
        severity: score > 0.9 ? 'critical' : 'warning',
        device: deviceId,
        user: device.owner,
        title: 'Anomalous Behavior Detected',
        message: `Unusual activity detected for device ${device.name}. Anomaly types: ${anomalies.map(a => a.type).join(', ')}`,
        data: {
          coordinates: {
            latitude: location.latitude,
            longitude: location.longitude
          },
          anomalyScore: score,
          anomalies: anomalies,
          deviceName: device.name
        },
        triggeredAt: new Date()
      };
      
      const alert = new Alert(alertData);
      await alert.save();
      
      logger.info(`Anomaly alert created for device ${device.deviceId}, score: ${score}`);
      
    } catch (error) {
      logger.error('Create anomaly alert error:', error);
    }
  }
}

module.exports = new AIService();
