const tf = require('@tensorflow/tfjs-node');
const { Matrix } = require('ml-matrix');
const ss = require('simple-statistics');
const brain = require('brain.js');
const config = require('config');
const logger = require('../../../../shared/utils/logger');
const database = require('../../../../shared/utils/database');

/**
 * Advanced AI Prediction Engine for GPS Tracking
 */
class PredictionEngine {
  constructor() {
    this.models = new Map();
    this.isInitialized = false;
    this.modelCache = new Map();
    this.predictionCache = new Map();
    this.trainingQueue = [];
    this.isTraining = false;
  }

  /**
   * Initialize the prediction engine
   */
  async initialize() {
    try {
      logger.info('Initializing AI Prediction Engine...');
      
      // Set TensorFlow backend configuration
      const tfConfig = config.get('ai.tensorflow');
      if (tfConfig.backend === 'cpu') {
        await tf.setBackend('cpu');
      }
      
      // Initialize pre-trained models
      await this.loadPretrainedModels();
      
      // Start background training scheduler
      this.startTrainingScheduler();
      
      this.isInitialized = true;
      logger.info('AI Prediction Engine initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize AI Prediction Engine', { error: error.message });
      throw error;
    }
  }

  /**
   * Load pre-trained models
   */
  async loadPretrainedModels() {
    try {
      // Route Prediction LSTM Model
      const routeModel = await this.createRoutePredictionModel();
      this.models.set('route_prediction', routeModel);
      
      // Anomaly Detection Model
      const anomalyModel = await this.createAnomalyDetectionModel();
      this.models.set('anomaly_detection', anomalyModel);
      
      // Behavior Classification Model
      const behaviorModel = await this.createBehaviorClassificationModel();
      this.models.set('behavior_classification', behaviorModel);
      
      // ETA Prediction Model
      const etaModel = await this.createETAPredictionModel();
      this.models.set('eta_prediction', etaModel);
      
      logger.info('Pre-trained models loaded successfully');
      
    } catch (error) {
      logger.error('Failed to load pre-trained models', { error: error.message });
      throw error;
    }
  }

  /**
   * Create LSTM model for route prediction
   */
  async createRoutePredictionModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.lstm({
          units: 128,
          returnSequences: true,
          inputShape: [10, 6], // 10 time steps, 6 features (lat, lng, speed, heading, time, day)
          dropout: 0.2,
          recurrentDropout: 0.2
        }),
        tf.layers.lstm({
          units: 64,
          returnSequences: false,
          dropout: 0.2,
          recurrentDropout: 0.2
        }),
        tf.layers.dense({
          units: 32,
          activation: 'relu'
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 16,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 2, // lat, lng prediction
          activation: 'linear'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * Create anomaly detection model using autoencoder
   */
  async createAnomalyDetectionModel() {
    const inputDim = 8; // speed, acceleration, heading_change, time_gap, distance, location_change, battery, signal
    
    const encoder = tf.sequential({
      layers: [
        tf.layers.dense({
          units: 16,
          activation: 'relu',
          inputShape: [inputDim]
        }),
        tf.layers.dense({
          units: 8,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 4,
          activation: 'relu'
        })
      ]
    });

    const decoder = tf.sequential({
      layers: [
        tf.layers.dense({
          units: 8,
          activation: 'relu',
          inputShape: [4]
        }),
        tf.layers.dense({
          units: 16,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: inputDim,
          activation: 'linear'
        })
      ]
    });

    const autoencoder = tf.sequential({
      layers: [encoder, decoder]
    });

    autoencoder.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError'
    });

    return { autoencoder, encoder, decoder };
  }

  /**
   * Create behavior classification model
   */
  async createBehaviorClassificationModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          units: 64,
          activation: 'relu',
          inputShape: [12] // speed_stats, time_patterns, location_patterns, movement_patterns
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 32,
          activation: 'relu'
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({
          units: 16,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 6, // normal, commuting, leisure, work, emergency, unknown
          activation: 'softmax'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  /**
   * Create ETA prediction model
   */
  async createETAPredictionModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          units: 128,
          activation: 'relu',
          inputShape: [10] // distance, avg_speed, traffic_factor, time_of_day, day_of_week, weather, route_complexity, historical_time, current_speed, acceleration
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 64,
          activation: 'relu'
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({
          units: 32,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 1, // ETA in minutes
          activation: 'linear'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * Predict next location using LSTM model
   */
  async predictNextLocation(deviceId, timeHorizon = 30) {
    try {
      const cacheKey = `route_prediction:${deviceId}:${timeHorizon}`;
      
      // Check cache first
      const cached = await this.getCachedPrediction(cacheKey);
      if (cached) {
        return cached;
      }

      // Get recent location data
      const locations = await this.getRecentLocations(deviceId, 50);
      
      if (locations.length < 10) {
        return {
          success: false,
          message: 'Insufficient data for prediction',
          confidence: 0
        };
      }

      // Prepare features
      const features = this.prepareRouteFeatures(locations);
      const inputTensor = tf.tensor3d([features], [1, features.length, features[0].length]);

      // Get model and predict
      const model = this.models.get('route_prediction');
      const prediction = await model.predict(inputTensor);
      const predictionData = await prediction.data();

      // Calculate confidence based on historical accuracy
      const confidence = await this.calculatePredictionConfidence(deviceId, locations);

      // Apply time horizon adjustment
      const adjustedPrediction = this.adjustPredictionForTimeHorizon(
        predictionData,
        locations[locations.length - 1],
        timeHorizon
      );

      const result = {
        success: true,
        prediction: {
          latitude: adjustedPrediction[1],
          longitude: adjustedPrediction[0],
          confidence,
          timeHorizon,
          estimatedTime: new Date(Date.now() + timeHorizon * 60 * 1000)
        },
        metadata: {
          dataPoints: locations.length,
          algorithm: 'lstm',
          modelVersion: '2.0'
        }
      };

      // Cache result
      await this.cachePrediction(cacheKey, result, 300); // 5 minutes

      // Cleanup tensors
      inputTensor.dispose();
      prediction.dispose();

      logger.ai('Route prediction', 'lstm', features.length, result, Date.now());

      return result;

    } catch (error) {
      logger.error('Route prediction failed', { 
        deviceId, 
        timeHorizon, 
        error: error.message 
      });
      
      return {
        success: false,
        message: 'Prediction failed',
        error: error.message
      };
    }
  }

  /**
   * Detect anomalies using autoencoder
   */
  async detectAnomalies(deviceId, currentLocation) {
    try {
      const cacheKey = `anomaly_detection:${deviceId}:${Date.now()}`;
      
      // Get historical data for comparison
      const historicalData = await this.getRecentLocations(deviceId, 100);
      
      if (historicalData.length < 20) {
        return {
          isAnomaly: false,
          confidence: 0,
          message: 'Insufficient historical data'
        };
      }

      // Prepare features for anomaly detection
      const features = this.prepareAnomalyFeatures(currentLocation, historicalData);
      const inputTensor = tf.tensor2d([features]);

      // Get autoencoder model
      const { autoencoder } = this.models.get('anomaly_detection');
      
      // Reconstruct input
      const reconstruction = await autoencoder.predict(inputTensor);
      const reconstructionData = await reconstruction.data();

      // Calculate reconstruction error
      const reconstructionError = this.calculateReconstructionError(features, reconstructionData);
      
      // Determine if anomaly based on threshold
      const threshold = config.get('ai.models.anomalyDetection.threshold');
      const isAnomaly = reconstructionError > threshold;
      
      // Calculate confidence score
      const confidence = Math.min(reconstructionError / threshold, 1.0);

      // Detailed anomaly analysis
      const anomalyDetails = await this.analyzeAnomalyDetails(currentLocation, historicalData);

      const result = {
        isAnomaly,
        confidence,
        reconstructionError,
        threshold,
        details: anomalyDetails,
        timestamp: new Date()
      };

      // Cleanup tensors
      inputTensor.dispose();
      reconstruction.dispose();

      logger.ai('Anomaly detection', 'autoencoder', features.length, result, Date.now());

      return result;

    } catch (error) {
      logger.error('Anomaly detection failed', { 
        deviceId, 
        error: error.message 
      });
      
      return {
        isAnomaly: false,
        confidence: 0,
        error: error.message
      };
    }
  }

  /**
   * Classify behavior patterns
   */
  async classifyBehavior(deviceId, timeWindow = 24) {
    try {
      // Get location data for the specified time window
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - timeWindow * 60 * 60 * 1000);
      
      const locations = await this.getLocationsByTimeRange(deviceId, startTime, endTime);
      
      if (locations.length < 10) {
        return {
          pattern: 'unknown',
          confidence: 0,
          message: 'Insufficient data for behavior classification'
        };
      }

      // Extract behavior features
      const features = this.extractBehaviorFeatures(locations);
      const inputTensor = tf.tensor2d([features]);

      // Get behavior classification model
      const model = this.models.get('behavior_classification');
      const prediction = await model.predict(inputTensor);
      const probabilities = await prediction.data();

      // Get the most likely behavior
      const behaviorClasses = ['normal', 'commuting', 'leisure', 'work', 'emergency', 'unknown'];
      const maxIndex = probabilities.indexOf(Math.max(...probabilities));
      const predictedBehavior = behaviorClasses[maxIndex];
      const confidence = probabilities[maxIndex];

      const result = {
        pattern: predictedBehavior,
        confidence,
        probabilities: Object.fromEntries(
          behaviorClasses.map((cls, idx) => [cls, probabilities[idx]])
        ),
        features: {
          speedVariation: features[0],
          timePatterns: features.slice(1, 5),
          locationClustering: features.slice(5, 8),
          movementPatterns: features.slice(8)
        }
      };

      // Cleanup tensors
      inputTensor.dispose();
      prediction.dispose();

      logger.ai('Behavior classification', 'neural_network', features.length, result, Date.now());

      return result;

    } catch (error) {
      logger.error('Behavior classification failed', { 
        deviceId, 
        timeWindow, 
        error: error.message 
      });
      
      return {
        pattern: 'unknown',
        confidence: 0,
        error: error.message
      };
    }
  }

  /**
   * Predict ETA to destination
   */
  async predictETA(deviceId, destinationLat, destinationLng) {
    try {
      // Get current location and recent movement data
      const recentLocations = await this.getRecentLocations(deviceId, 10);
      
      if (recentLocations.length === 0) {
        return {
          success: false,
          message: 'No recent location data available'
        };
      }

      const currentLocation = recentLocations[0];
      
      // Calculate distance to destination
      const distance = this.calculateDistance(
        currentLocation.location.coordinates,
        [destinationLng, destinationLat]
      );

      // Extract ETA features
      const features = await this.extractETAFeatures(
        currentLocation,
        recentLocations,
        distance,
        destinationLat,
        destinationLng
      );

      const inputTensor = tf.tensor2d([features]);

      // Get ETA prediction model
      const model = this.models.get('eta_prediction');
      const prediction = await model.predict(inputTensor);
      const etaMinutes = (await prediction.data())[0];

      // Calculate confidence based on model accuracy and data quality
      const confidence = this.calculateETAConfidence(recentLocations, distance);

      const result = {
        success: true,
        eta: {
          minutes: Math.max(1, Math.round(etaMinutes)),
          estimatedArrival: new Date(Date.now() + etaMinutes * 60 * 1000),
          confidence,
          distance: Math.round(distance),
          averageSpeed: this.calculateAverageSpeed(recentLocations)
        },
        route: {
          startLocation: {
            latitude: currentLocation.location.coordinates[1],
            longitude: currentLocation.location.coordinates[0]
          },
          endLocation: {
            latitude: destinationLat,
            longitude: destinationLng
          }
        }
      };

      // Cleanup tensors
      inputTensor.dispose();
      prediction.dispose();

      logger.ai('ETA prediction', 'neural_network', features.length, result, Date.now());

      return result;

    } catch (error) {
      logger.error('ETA prediction failed', { 
        deviceId, 
        destinationLat, 
        destinationLng, 
        error: error.message 
      });
      
      return {
        success: false,
        message: 'ETA prediction failed',
        error: error.message
      };
    }
  }

  /**
   * Prepare features for route prediction
   */
  prepareRouteFeatures(locations) {
    const features = [];
    
    for (let i = Math.max(0, locations.length - 10); i < locations.length; i++) {
      const loc = locations[i];
      const timestamp = new Date(loc.timestamp);
      
      features.push([
        loc.location.coordinates[1], // latitude
        loc.location.coordinates[0], // longitude
        loc.speed || 0,
        loc.heading || 0,
        timestamp.getHours() / 24, // normalized hour
        timestamp.getDay() / 7 // normalized day of week
      ]);
    }
    
    // Pad with zeros if not enough data
    while (features.length < 10) {
      features.unshift([0, 0, 0, 0, 0, 0]);
    }
    
    return features;
  }

  /**
   * Prepare features for anomaly detection
   */
  prepareAnomalyFeatures(currentLocation, historicalData) {
    const recent = historicalData.slice(-5);
    
    // Calculate features
    const speed = currentLocation.speed || 0;
    const acceleration = this.calculateAcceleration(recent);
    const headingChange = this.calculateHeadingChange(recent);
    const timeGap = this.calculateTimeGap(recent);
    const distance = this.calculateDistanceFromRecent(currentLocation, recent);
    const locationChange = this.calculateLocationChange(recent);
    const battery = currentLocation.deviceStatus?.batteryLevel || 100;
    const signal = currentLocation.deviceStatus?.signalStrength || -50;
    
    return [speed, acceleration, headingChange, timeGap, distance, locationChange, battery, signal];
  }

  /**
   * Extract behavior features from location data
   */
  extractBehaviorFeatures(locations) {
    const speeds = locations.map(l => l.speed || 0).filter(s => s > 0);
    const timestamps = locations.map(l => new Date(l.timestamp));
    
    // Speed variation features
    const speedVariation = speeds.length > 0 ? ss.standardDeviation(speeds) / ss.mean(speeds) : 0;
    
    // Time pattern features
    const hourCounts = new Array(24).fill(0);
    timestamps.forEach(t => hourCounts[t.getHours()]++);
    const peakHours = hourCounts.map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 4)
      .map(h => h.hour / 24);
    
    // Location clustering features
    const coordinates = locations.map(l => l.location.coordinates);
    const locationClusters = this.performSimpleClustering(coordinates, 3);
    
    // Movement pattern features
    const distances = this.calculateDistances(locations);
    const totalDistance = distances.reduce((sum, d) => sum + d, 0);
    const avgDistance = totalDistance / distances.length || 0;
    const maxDistance = Math.max(...distances, 0);
    const stationaryTime = this.calculateStationaryTime(locations);
    
    return [
      speedVariation,
      ...peakHours,
      locationClusters.length,
      locationClusters[0]?.size || 0,
      locationClusters[1]?.size || 0,
      avgDistance,
      maxDistance,
      stationaryTime,
      totalDistance
    ];
  }

  /**
   * Extract ETA features
   */
  async extractETAFeatures(currentLocation, recentLocations, distance, destLat, destLng) {
    const avgSpeed = this.calculateAverageSpeed(recentLocations);
    const currentSpeed = currentLocation.speed || 0;
    const acceleration = this.calculateAcceleration(recentLocations);
    
    const now = new Date();
    const timeOfDay = now.getHours() / 24;
    const dayOfWeek = now.getDay() / 7;
    
    // Traffic factor (simplified - in production, use real traffic data)
    const trafficFactor = this.estimateTrafficFactor(now);
    
    // Route complexity (based on historical data)
    const routeComplexity = await this.calculateRouteComplexity(
      currentLocation.location.coordinates,
      [destLng, destLat]
    );
    
    // Historical time for similar routes
    const historicalTime = await this.getHistoricalTravelTime(
      currentLocation.location.coordinates,
      [destLng, destLat]
    );
    
    return [
      distance / 1000, // distance in km
      avgSpeed,
      trafficFactor,
      timeOfDay,
      dayOfWeek,
      0.5, // weather factor (placeholder)
      routeComplexity,
      historicalTime,
      currentSpeed,
      acceleration
    ];
  }

  /**
   * Start background training scheduler
   */
  startTrainingScheduler() {
    // Retrain models every 6 hours
    setInterval(async () => {
      if (!this.isTraining) {
        await this.retrainModels();
      }
    }, 6 * 60 * 60 * 1000);
    
    logger.info('AI training scheduler started');
  }

  /**
   * Retrain models with new data
   */
  async retrainModels() {
    try {
      this.isTraining = true;
      logger.info('Starting model retraining...');
      
      // Retrain route prediction model
      await this.retrainRoutePredictionModel();
      
      // Retrain anomaly detection model
      await this.retrainAnomalyDetectionModel();
      
      // Retrain behavior classification model
      await this.retrainBehaviorClassificationModel();
      
      logger.info('Model retraining completed successfully');
      
    } catch (error) {
      logger.error('Model retraining failed', { error: error.message });
    } finally {
      this.isTraining = false;
    }
  }

  /**
   * Helper methods for calculations
   */
  calculateDistance(coord1, coord2) {
    const R = 6371000; // Earth's radius in meters
    const lat1 = coord1[1] * Math.PI / 180;
    const lat2 = coord2[1] * Math.PI / 180;
    const deltaLat = (coord2[1] - coord1[1]) * Math.PI / 180;
    const deltaLon = (coord2[0] - coord1[0]) * Math.PI / 180;
    
    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c;
  }

  calculateAcceleration(locations) {
    if (locations.length < 2) return 0;
    
    const recent = locations.slice(-2);
    const speedDiff = (recent[1].speed || 0) - (recent[0].speed || 0);
    const timeDiff = (new Date(recent[1].timestamp) - new Date(recent[0].timestamp)) / 1000;
    
    return timeDiff > 0 ? speedDiff / timeDiff : 0;
  }

  calculateAverageSpeed(locations) {
    const speeds = locations.map(l => l.speed || 0).filter(s => s > 0);
    return speeds.length > 0 ? ss.mean(speeds) : 0;
  }

  async getCachedPrediction(key) {
    try {
      const redis = database.getRedis();
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      return null;
    }
  }

  async cachePrediction(key, data, ttl) {
    try {
      const redis = database.getRedis();
      await redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      logger.error('Failed to cache prediction', { key, error: error.message });
    }
  }

  // Placeholder methods - implement based on your data access patterns
  async getRecentLocations(deviceId, limit) {
    // Implement database query to get recent locations
    return [];
  }

  async getLocationsByTimeRange(deviceId, startTime, endTime) {
    // Implement database query to get locations by time range
    return [];
  }
}

module.exports = PredictionEngine;
