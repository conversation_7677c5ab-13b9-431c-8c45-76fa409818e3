apiVersion: v1
kind: ConfigMap
metadata:
  name: gps-tracker-config
  namespace: gps-tracker-prod
data:
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  
  # Database Configuration
  MONGODB_URI: "mongodb://mongodb-service:27017/enterprise-gps-tracker"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # Service URLs
  AUTH_SERVICE_URL: "http://auth-service:3001"
  DEVICE_SERVICE_URL: "http://device-service:3002"
  LOCATION_SERVICE_URL: "http://location-service:3003"
  AI_SERVICE_URL: "http://ai-service:3004"
  NOTIFICATION_SERVICE_URL: "http://notification-service:3005"
  
  # AI Configuration
  TF_NUM_THREADS: "4"
  TF_MEMORY_LIMIT: "1024"
  
  # Monitoring
  METRICS_PORT: "9090"
  HEALTH_CHECK_PORT: "8080"
  ELASTICSEARCH_ENABLED: "true"
  JAEGER_ENDPOINT: "http://jaeger-collector:14268/api/traces"
  
  # Rate Limiting
  RATE_LIMIT_WINDOW_MS: "900000"
  RATE_LIMIT_MAX_REQUESTS: "1000"
  
  # Cache Configuration
  CACHE_TTL_DEFAULT: "300"
  CACHE_TTL_USER: "1800"
  CACHE_TTL_DEVICE: "900"
  CACHE_TTL_LOCATION: "60"
  
  # Security
  CORS_ORIGIN: "*"
  HELMET_ENABLED: "true"
  
  # Performance
  COMPRESSION_ENABLED: "true"
  COMPRESSION_LEVEL: "6"
  
  # Data Retention
  LOCATION_RETENTION_DAYS: "365"
  LOG_RETENTION_DAYS: "90"
  METRICS_RETENTION_DAYS: "30"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: gps-tracker-prod
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for" '
                        'rt=$request_time uct="$upstream_connect_time" '
                        'uht="$upstream_header_time" urt="$upstream_response_time"';

        access_log /var/log/nginx/access.log main;

        # Performance
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 10M;

        # Gzip
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

        # Upstream services
        upstream api_gateway {
            least_conn;
            server api-gateway-service:3000 max_fails=3 fail_timeout=30s;
        }

        # Main server block
        server {
            listen 80;
            server_name _;

            # Security headers
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

            # Health check
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # API routes
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                proxy_pass http://api_gateway;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 5s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
            }

            # Auth endpoints with stricter rate limiting
            location /api/auth/ {
                limit_req zone=auth burst=10 nodelay;
                proxy_pass http://api_gateway;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # WebSocket support
            location /socket.io/ {
                proxy_pass http://api_gateway;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Static files
            location / {
                root /usr/share/nginx/html;
                index index.html;
                try_files $uri $uri/ /index.html;
                expires 1h;
                add_header Cache-Control "public, immutable";
            }

            # Error pages
            error_page 404 /404.html;
            error_page 500 502 503 504 /50x.html;
            location = /50x.html {
                root /usr/share/nginx/html;
            }
        }
    }
