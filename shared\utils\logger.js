const winston = require('winston');
const ElasticsearchTransport = require('winston-elasticsearch');
const config = require('config');
const path = require('path');

/**
 * Enterprise Logger with multiple transports and structured logging
 */
class Logger {
  constructor() {
    this.logger = this.createLogger();
    this.setupUncaughtExceptionHandlers();
  }

  /**
   * Create Winston logger with multiple transports
   */
  createLogger() {
    const logConfig = config.get('logging');
    const transports = [];

    // Console transport
    if (logConfig.transports.console.enabled) {
      transports.push(new winston.transports.Console({
        level: logConfig.transports.console.level,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }),
          winston.format.colorize({ all: logConfig.transports.console.colorize }),
          winston.format.printf(({ timestamp, level, message, service, traceId, ...meta }) => {
            let log = `${timestamp} [${level}]`;
            if (service) log += ` [${service}]`;
            if (traceId) log += ` [${traceId}]`;
            log += `: ${message}`;
            
            if (Object.keys(meta).length > 0) {
              log += ` ${JSON.stringify(meta)}`;
            }
            return log;
          })
        )
      }));
    }

    // File transport
    if (logConfig.transports.file.enabled) {
      transports.push(new winston.transports.File({
        level: logConfig.transports.file.level,
        filename: logConfig.transports.file.filename,
        maxsize: logConfig.transports.file.maxsize,
        maxFiles: logConfig.transports.file.maxFiles,
        tailable: logConfig.transports.file.tailable,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }),
          winston.format.json()
        )
      }));
    }

    // Elasticsearch transport
    if (logConfig.transports.elasticsearch.enabled) {
      transports.push(new ElasticsearchTransport({
        level: logConfig.transports.elasticsearch.level,
        clientOpts: {
          node: logConfig.transports.elasticsearch.host
        },
        index: logConfig.transports.elasticsearch.index,
        transformer: (logData) => {
          return {
            '@timestamp': new Date().toISOString(),
            severity: logData.level,
            message: logData.message,
            service: logData.service || 'unknown',
            environment: config.get('app.environment'),
            version: config.get('app.version'),
            ...logData.meta
          };
        }
      }));
    }

    return winston.createLogger({
      level: logConfig.level,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.metadata({ fillExcept: ['message', 'level', 'timestamp'] })
      ),
      defaultMeta: {
        service: process.env.SERVICE_NAME || 'gps-tracker',
        environment: config.get('app.environment'),
        version: config.get('app.version'),
        hostname: require('os').hostname(),
        pid: process.pid
      },
      transports,
      exitOnError: false
    });
  }

  /**
   * Setup uncaught exception handlers
   */
  setupUncaughtExceptionHandlers() {
    this.logger.exceptions.handle(
      new winston.transports.File({ 
        filename: 'logs/exceptions.log',
        maxsize: 10485760,
        maxFiles: 5
      })
    );

    this.logger.rejections.handle(
      new winston.transports.File({ 
        filename: 'logs/rejections.log',
        maxsize: 10485760,
        maxFiles: 5
      })
    );
  }

  /**
   * Create child logger with additional context
   */
  child(context) {
    return {
      info: (message, meta = {}) => this.info(message, { ...context, ...meta }),
      warn: (message, meta = {}) => this.warn(message, { ...context, ...meta }),
      error: (message, meta = {}) => this.error(message, { ...context, ...meta }),
      debug: (message, meta = {}) => this.debug(message, { ...context, ...meta })
    };
  }

  /**
   * Log info message
   */
  info(message, meta = {}) {
    this.logger.info(message, this.enrichMeta(meta));
  }

  /**
   * Log warning message
   */
  warn(message, meta = {}) {
    this.logger.warn(message, this.enrichMeta(meta));
  }

  /**
   * Log error message
   */
  error(message, meta = {}) {
    this.logger.error(message, this.enrichMeta(meta));
  }

  /**
   * Log debug message
   */
  debug(message, meta = {}) {
    this.logger.debug(message, this.enrichMeta(meta));
  }

  /**
   * Log HTTP request
   */
  http(req, res, responseTime) {
    const meta = {
      method: req.method,
      url: req.originalUrl || req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      userId: req.user?.id,
      traceId: req.traceId
    };

    const level = res.statusCode >= 400 ? 'warn' : 'info';
    this[level](`${req.method} ${req.originalUrl || req.url} ${res.statusCode} ${responseTime}ms`, meta);
  }

  /**
   * Log database operation
   */
  database(operation, collection, query, result, duration) {
    this.debug('Database operation', {
      operation,
      collection,
      query: JSON.stringify(query),
      resultCount: Array.isArray(result) ? result.length : result ? 1 : 0,
      duration: `${duration}ms`,
      type: 'database'
    });
  }

  /**
   * Log AI operation
   */
  ai(operation, model, input, output, duration, accuracy) {
    this.info('AI operation', {
      operation,
      model,
      inputSize: typeof input === 'object' ? Object.keys(input).length : 1,
      outputSize: typeof output === 'object' ? Object.keys(output).length : 1,
      duration: `${duration}ms`,
      accuracy,
      type: 'ai'
    });
  }

  /**
   * Log security event
   */
  security(event, details, severity = 'warn') {
    this[severity](`Security event: ${event}`, {
      ...details,
      type: 'security',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log performance metric
   */
  performance(metric, value, unit = 'ms', tags = {}) {
    this.info(`Performance metric: ${metric}`, {
      metric,
      value,
      unit,
      tags,
      type: 'performance'
    });
  }

  /**
   * Log business event
   */
  business(event, data = {}) {
    this.info(`Business event: ${event}`, {
      ...data,
      type: 'business',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Enrich metadata with common fields
   */
  enrichMeta(meta) {
    return {
      ...meta,
      timestamp: new Date().toISOString(),
      correlationId: meta.correlationId || this.generateCorrelationId()
    };
  }

  /**
   * Generate correlation ID for request tracking
   */
  generateCorrelationId() {
    return require('uuid').v4();
  }

  /**
   * Create audit log entry
   */
  audit(action, resource, user, details = {}) {
    this.info(`Audit: ${action} on ${resource}`, {
      action,
      resource,
      userId: user?.id,
      username: user?.username,
      userRole: user?.role,
      ...details,
      type: 'audit'
    });
  }

  /**
   * Log system health check
   */
  health(component, status, details = {}) {
    const level = status === 'healthy' ? 'info' : 'warn';
    this[level](`Health check: ${component} is ${status}`, {
      component,
      status,
      ...details,
      type: 'health'
    });
  }

  /**
   * Create structured error log
   */
  logError(error, context = {}) {
    this.error(error.message || 'Unknown error', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code
      },
      ...context,
      type: 'error'
    });
  }

  /**
   * Log API rate limit hit
   */
  rateLimit(ip, endpoint, limit, window) {
    this.warn('Rate limit exceeded', {
      ip,
      endpoint,
      limit,
      window,
      type: 'rateLimit'
    });
  }

  /**
   * Log cache operation
   */
  cache(operation, key, hit, duration) {
    this.debug(`Cache ${operation}`, {
      operation,
      key,
      hit,
      duration: duration ? `${duration}ms` : undefined,
      type: 'cache'
    });
  }

  /**
   * Log queue operation
   */
  queue(operation, queue, job, status, duration) {
    this.info(`Queue ${operation}`, {
      operation,
      queue,
      jobId: job?.id,
      jobType: job?.name,
      status,
      duration: duration ? `${duration}ms` : undefined,
      type: 'queue'
    });
  }
}

// Create singleton instance
const logger = new Logger();

// Export logger methods
module.exports = {
  info: logger.info.bind(logger),
  warn: logger.warn.bind(logger),
  error: logger.error.bind(logger),
  debug: logger.debug.bind(logger),
  http: logger.http.bind(logger),
  database: logger.database.bind(logger),
  ai: logger.ai.bind(logger),
  security: logger.security.bind(logger),
  performance: logger.performance.bind(logger),
  business: logger.business.bind(logger),
  audit: logger.audit.bind(logger),
  health: logger.health.bind(logger),
  logError: logger.logError.bind(logger),
  rateLimit: logger.rateLimit.bind(logger),
  cache: logger.cache.bind(logger),
  queue: logger.queue.bind(logger),
  child: logger.child.bind(logger),
  logger: logger.logger
};
