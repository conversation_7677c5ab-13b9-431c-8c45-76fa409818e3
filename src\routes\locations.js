const express = require('express');
const LocationController = require('../controllers/locationController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const SecurityMiddleware = require('../middleware/security');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Location:
 *       type: object
 *       required:
 *         - latitude
 *         - longitude
 *       properties:
 *         latitude:
 *           type: number
 *           minimum: -90
 *           maximum: 90
 *           description: Latitude coordinate
 *         longitude:
 *           type: number
 *           minimum: -180
 *           maximum: 180
 *           description: Longitude coordinate
 *         accuracy:
 *           type: number
 *           minimum: 0
 *           description: Location accuracy in meters
 *         altitude:
 *           type: number
 *           description: Altitude in meters
 *         speed:
 *           type: number
 *           minimum: 0
 *           description: Speed in km/h
 *         heading:
 *           type: number
 *           minimum: 0
 *           maximum: 360
 *           description: Heading in degrees
 *         batteryLevel:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           description: Battery level percentage
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Location timestamp
 *         address:
 *           type: string
 *           description: Human-readable address
 */

/**
 * @swagger
 * /api/locations/update:
 *   post:
 *     summary: Update device location (for devices)
 *     tags: [Locations]
 *     security:
 *       - apiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Location'
 *     responses:
 *       200:
 *         description: Location updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     locationId:
 *                       type: string
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Validation error
 *       401:
 *         description: Invalid API key
 *       429:
 *         description: Rate limit exceeded
 *       500:
 *         description: Internal server error
 */
router.post('/update',
  SecurityMiddleware.locationUpdateRateLimit(),
  AuthMiddleware.validateApiKey,
  ValidationMiddleware.validateLocationUpdate(),
  LocationController.updateLocation
);

/**
 * @swagger
 * /api/locations/devices/{id}/history:
 *   get:
 *     summary: Get location history for a device
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for history
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for history
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *           default: 100
 *         description: Number of locations to return
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *     responses:
 *       200:
 *         description: Location history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     locations:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Location'
 *                     pagination:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.get('/devices/:id/history',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  ValidationMiddleware.validateLocationQuery(),
  LocationController.getLocationHistory
);

/**
 * @swagger
 * /api/locations/devices/{id}/current:
 *   get:
 *     summary: Get current location for a device
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     responses:
 *       200:
 *         description: Current location retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     location:
 *                       allOf:
 *                         - $ref: '#/components/schemas/Location'
 *                         - type: object
 *                           properties:
 *                             deviceId:
 *                               type: string
 *                             deviceName:
 *                               type: string
 *                             isOnline:
 *                               type: boolean
 *                             lastSeen:
 *                               type: string
 *                               format: date-time
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device or location not found
 *       500:
 *         description: Internal server error
 */
router.get('/devices/:id/current',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  LocationController.getCurrentLocation
);

/**
 * @swagger
 * /api/locations/devices/{id}/area:
 *   get:
 *     summary: Get locations within a geographic area
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *       - in: query
 *         name: latitude
 *         required: true
 *         schema:
 *           type: number
 *           minimum: -90
 *           maximum: 90
 *         description: Center latitude
 *       - in: query
 *         name: longitude
 *         required: true
 *         schema:
 *           type: number
 *           minimum: -180
 *           maximum: 180
 *         description: Center longitude
 *       - in: query
 *         name: radius
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100000
 *           default: 1000
 *         description: Search radius in meters
 *     responses:
 *       200:
 *         description: Locations in area retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     locations:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Location'
 *                     center:
 *                       type: object
 *                       properties:
 *                         latitude:
 *                           type: number
 *                         longitude:
 *                           type: number
 *                     radius:
 *                       type: integer
 *                     count:
 *                       type: integer
 *       400:
 *         description: Missing required parameters
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 *       500:
 *         description: Internal server error
 */
router.get('/devices/:id/area',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateObjectId('id'),
  [
    ValidationMiddleware.query('latitude')
      .isFloat({ min: -90, max: 90 })
      .withMessage('Latitude must be between -90 and 90'),
    ValidationMiddleware.query('longitude')
      .isFloat({ min: -180, max: 180 })
      .withMessage('Longitude must be between -180 and 180'),
    ValidationMiddleware.query('radius')
      .optional()
      .isInt({ min: 1, max: 100000 })
      .withMessage('Radius must be between 1 and 100000 meters'),
    ValidationMiddleware.handleValidationErrors
  ],
  LocationController.getLocationsInArea
);

module.exports = router;
