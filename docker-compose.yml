version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=mongodb://mongodb:27017/enterprise-gps-tracker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:3001
      - DEVICE_SERVICE_URL=http://device-service:3002
      - LOCATION_SERVICE_URL=http://location-service:3003
      - AI_SERVICE_URL=http://ai-service:3004
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
    depends_on:
      - mongodb
      - redis
      - auth-service
      - device-service
      - location-service
      - ai-service
    networks:
      - gps-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Authentication Service
  auth-service:
    build:
      context: .
      dockerfile: services/auth-service/Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb:27017/enterprise-gps-tracker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_REFRESH_SECRET=your-refresh-secret-change-in-production
    depends_on:
      - mongodb
      - redis
    networks:
      - gps-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Device Management Service
  device-service:
    build:
      context: .
      dockerfile: services/device-service/Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - MONGODB_URI=mongodb://mongodb:27017/enterprise-gps-tracker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - mongodb
      - redis
      - auth-service
    networks:
      - gps-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Location Tracking Service
  location-service:
    build:
      context: .
      dockerfile: services/location-service/Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - MONGODB_URI=mongodb://mongodb:27017/enterprise-gps-tracker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DEVICE_SERVICE_URL=http://device-service:3002
      - AI_SERVICE_URL=http://ai-service:3004
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
    depends_on:
      - mongodb
      - redis
      - device-service
    networks:
      - gps-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI/ML Service
  ai-service:
    build:
      context: .
      dockerfile: services/ai-service/Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - MONGODB_URI=mongodb://mongodb:27017/enterprise-gps-tracker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TF_NUM_THREADS=4
      - TF_MEMORY_LIMIT=1024
    depends_on:
      - mongodb
      - redis
    networks:
      - gps-network
    volumes:
      - ./logs:/app/logs
      - ./ai-models:/app/models
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Notification Service
  notification-service:
    build:
      context: .
      dockerfile: services/notification-service/Dockerfile
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - MONGODB_URI=mongodb://mongodb:27017/enterprise-gps-tracker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - EMAIL_ENABLED=true
      - EMAIL_SERVICE=gmail
      - SMS_ENABLED=false
      - PUSH_ENABLED=false
    depends_on:
      - mongodb
      - redis
    networks:
      - gps-network
    volumes:
      - ./logs:/app/logs
      - ./templates:/app/templates
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=enterprise-gps-tracker
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - gps-network
    restart: unless-stopped
    command: mongod --replSet rs0 --bind_ip_all
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache & Message Broker
  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - gps-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - gps-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    depends_on:
      - elasticsearch
    networks:
      - gps-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - gps-network
    restart: unless-stopped

  # Grafana for Monitoring Dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - gps-network
    restart: unless-stopped

  # Jaeger for Distributed Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - gps-network
    restart: unless-stopped

  # NGINX Load Balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
      - ./public:/usr/share/nginx/html
    depends_on:
      - api-gateway
    networks:
      - gps-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Message Queue (Bull Dashboard)
  bull-dashboard:
    image: deadly0/bull-board
    ports:
      - "3006:3000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis123
    depends_on:
      - redis
    networks:
      - gps-network
    restart: unless-stopped

networks:
  gps-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
