const request = require('supertest');
const mongoose = require('mongoose');
const server = require('../src/server');

describe('AI GPS Tracker API Tests', () => {
  let authToken;
  let testUser;
  let testDevice;

  beforeAll(async () => {
    // Connect to test database
    const testDbUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/ai-gps-tracker-test';
    await mongoose.connect(testDbUri);
  });

  afterAll(async () => {
    // Clean up test database
    await mongoose.connection.db.dropDatabase();
    await mongoose.connection.close();
  });

  describe('Authentication', () => {
    test('POST /api/auth/register - should register a new user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Test123!',
        firstName: 'Test',
        lastName: 'User'
      };

      const response = await request(server.app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.tokens.accessToken).toBeDefined();

      // Store for later tests
      authToken = response.body.data.tokens.accessToken;
      testUser = response.body.data.user;
    });

    test('POST /api/auth/login - should login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Test123!'
      };

      const response = await request(server.app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokens.accessToken).toBeDefined();
    });

    test('POST /api/auth/login - should fail with invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(server.app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('GET /api/auth/profile - should get user profile', async () => {
      const response = await request(server.app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });
  });

  describe('Device Management', () => {
    test('POST /api/devices - should register a new device', async () => {
      const deviceData = {
        deviceId: 'TEST001',
        name: 'Test Device',
        type: 'vehicle',
        description: 'Test GPS device'
      };

      const response = await request(server.app)
        .post('/api/devices')
        .set('Authorization', `Bearer ${authToken}`)
        .send(deviceData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.device.deviceId).toBe(deviceData.deviceId);

      // Store for later tests
      testDevice = response.body.data.device;
    });

    test('GET /api/devices - should get user devices', async () => {
      const response = await request(server.app)
        .get('/api/devices')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.devices).toHaveLength(1);
      expect(response.body.data.devices[0].deviceId).toBe('TEST001');
    });

    test('GET /api/devices/:id - should get specific device', async () => {
      const response = await request(server.app)
        .get(`/api/devices/${testDevice._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.device._id).toBe(testDevice._id);
    });

    test('PUT /api/devices/:id - should update device', async () => {
      const updateData = {
        name: 'Updated Test Device',
        description: 'Updated description'
      };

      const response = await request(server.app)
        .put(`/api/devices/${testDevice._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.device.name).toBe(updateData.name);
    });
  });

  describe('Location Tracking', () => {
    test('POST /api/locations/update - should update device location', async () => {
      const locationData = {
        latitude: 40.7128,
        longitude: -74.0060,
        accuracy: 10,
        speed: 25,
        heading: 180,
        batteryLevel: 85
      };

      const response = await request(server.app)
        .post('/api/locations/update')
        .set('X-API-Key', testDevice.deviceId)
        .send(locationData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.locationId).toBeDefined();
    });

    test('GET /api/locations/devices/:id/current - should get current location', async () => {
      const response = await request(server.app)
        .get(`/api/locations/devices/${testDevice._id}/current`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.location.latitude).toBe(40.7128);
      expect(response.body.data.location.longitude).toBe(-74.0060);
    });

    test('GET /api/locations/devices/:id/history - should get location history', async () => {
      const response = await request(server.app)
        .get(`/api/locations/devices/${testDevice._id}/history`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.locations).toHaveLength(1);
    });
  });

  describe('AI Features', () => {
    test('GET /api/ai/devices/:id/predict-route - should get route prediction', async () => {
      // Add more location data for better prediction
      const locations = [
        { latitude: 40.7128, longitude: -74.0060, speed: 25 },
        { latitude: 40.7130, longitude: -74.0058, speed: 30 },
        { latitude: 40.7132, longitude: -74.0056, speed: 28 },
        { latitude: 40.7134, longitude: -74.0054, speed: 32 },
        { latitude: 40.7136, longitude: -74.0052, speed: 29 }
      ];

      // Add multiple location updates
      for (const location of locations) {
        await request(server.app)
          .post('/api/locations/update')
          .set('X-API-Key', testDevice.deviceId)
          .send(location);
      }

      const response = await request(server.app)
        .get(`/api/ai/devices/${testDevice._id}/predict-route?timeHorizon=30`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      if (response.body.data.prediction) {
        expect(response.body.data.prediction.latitude).toBeDefined();
        expect(response.body.data.prediction.longitude).toBeDefined();
        expect(response.body.data.prediction.confidence).toBeDefined();
      }
    });

    test('POST /api/ai/devices/:id/analyze-anomaly - should analyze location anomaly', async () => {
      const anomalyData = {
        latitude: 41.0000, // Far from previous locations
        longitude: -75.0000,
        speed: 150, // Very high speed
        heading: 90
      };

      const response = await request(server.app)
        .post(`/api/ai/devices/${testDevice._id}/analyze-anomaly`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(anomalyData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.analysis.isAnomaly).toBeDefined();
      expect(response.body.data.analysis.confidence).toBeDefined();
    });

    test('GET /api/ai/devices/:id/insights - should get device insights', async () => {
      const response = await request(server.app)
        .get(`/api/ai/devices/${testDevice._id}/insights?days=7`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.insights).toBeDefined();
      expect(response.body.data.insights.dataPoints).toBeGreaterThan(0);
    });

    test('GET /api/ai/devices/:id/movement-patterns - should get movement patterns', async () => {
      const response = await request(server.app)
        .get(`/api/ai/devices/${testDevice._id}/movement-patterns?days=30`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.patterns).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should return 401 for unauthorized requests', async () => {
      const response = await request(server.app)
        .get('/api/devices')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should return 404 for non-existent device', async () => {
      const fakeId = new mongoose.Types.ObjectId();
      
      const response = await request(server.app)
        .get(`/api/devices/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    test('should validate input data', async () => {
      const invalidDeviceData = {
        deviceId: 'ab', // Too short
        name: '', // Empty
        type: 'invalid' // Invalid type
      };

      const response = await request(server.app)
        .post('/api/devices')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidDeviceData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Health Check', () => {
    test('GET /health - should return health status', async () => {
      const response = await request(server.app)
        .get('/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('running');
    });
  });
});
