require("dotenv").config();

const express = require("express");
const http = require("http");
const socketIo = require("socket.io");
const morgan = require("morgan");
const compression = require("compression");
const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");

// Import configurations and middleware
const database = require("./config/database");
const logger = require("./utils/logger");
const SecurityMiddleware = require("./middleware/security");

// Import routes
const authRoutes = require("./routes/auth");
const deviceRoutes = require("./routes/devices");
const locationRoutes = require("./routes/locations");
const aiRoutes = require("./routes/ai");

/**
 * AI GPS Tracker Server
 */
class GPSTrackerServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server, {
      cors: {
        origin: process.env.SOCKET_IO_CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"],
      },
    });
    this.port = process.env.PORT || 3000;

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeSwagger();
    this.initializeSocketIO();
    this.initializeErrorHandling();
  }

  /**
   * Initialize middleware
   */
  initializeMiddleware() {
    // Security middleware
    this.app.use(SecurityMiddleware.configureHelmet());
    this.app.use(SecurityMiddleware.configureCORS());
    this.app.use(SecurityMiddleware.securityHeaders);

    // Rate limiting
    this.app.use("/api/", SecurityMiddleware.apiRateLimit());

    // Body parsing and compression
    this.app.use(compression());
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Input sanitization
    this.app.use(SecurityMiddleware.sanitizeInput);

    // Logging
    if (process.env.NODE_ENV !== "test") {
      this.app.use(
        morgan("combined", {
          stream: {
            write: (message) => logger.info(message.trim()),
          },
        })
      );
    }

    this.app.use(SecurityMiddleware.requestLogger);

    // Static files
    this.app.use(express.static("public"));

    // Make Socket.IO available to routes
    this.app.set("io", this.io);
  }

  /**
   * Initialize API routes
   */
  initializeRoutes() {
    // Health check endpoint
    this.app.get("/health", (req, res) => {
      res.json({
        success: true,
        message: "AI GPS Tracker API is running",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        environment: process.env.NODE_ENV || "development",
      });
    });

    // API routes
    this.app.use("/api/auth", authRoutes);
    this.app.use("/api/devices", deviceRoutes);
    this.app.use("/api/locations", locationRoutes);
    this.app.use("/api/ai", aiRoutes);

    // 404 handler for API routes
    this.app.use("/api/*", (req, res) => {
      res.status(404).json({
        success: false,
        message: "API endpoint not found",
        path: req.originalUrl,
      });
    });

    // Root endpoint
    this.app.get("/", (req, res) => {
      res.json({
        success: true,
        message: "Welcome to AI GPS Tracker API",
        version: "1.0.0",
        documentation: "/api-docs",
        endpoints: {
          health: "/health",
          auth: "/api/auth",
          devices: "/api/devices",
          locations: "/api/locations",
        },
      });
    });
  }

  /**
   * Initialize Swagger documentation
   */
  initializeSwagger() {
    const swaggerOptions = {
      definition: {
        openapi: "3.0.0",
        info: {
          title: "AI GPS Tracker API",
          version: "1.0.0",
          description:
            "Advanced AI-powered GPS tracking system with real-time monitoring, geofencing, and intelligent analytics",
          contact: {
            name: "API Support",
            email: "<EMAIL>",
          },
        },
        servers: [
          {
            url:
              process.env.NODE_ENV === "production"
                ? "https://your-domain.com"
                : `http://localhost:${this.port}`,
            description:
              process.env.NODE_ENV === "production"
                ? "Production server"
                : "Development server",
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: "http",
              scheme: "bearer",
              bearerFormat: "JWT",
            },
            apiKeyAuth: {
              type: "apiKey",
              in: "header",
              name: "X-API-Key",
            },
          },
        },
      },
      apis: ["./src/routes/*.js"], // Path to the API files
    };

    const swaggerSpec = swaggerJsdoc(swaggerOptions);

    this.app.use(
      "/api-docs",
      swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        customCss: ".swagger-ui .topbar { display: none }",
        customSiteTitle: "AI GPS Tracker API Documentation",
      })
    );

    // Serve swagger spec as JSON
    this.app.get("/api-docs.json", (req, res) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });
  }

  /**
   * Initialize Socket.IO for real-time communication
   */
  initializeSocketIO() {
    this.io.on("connection", (socket) => {
      logger.info(`Socket connected: ${socket.id}`);

      // Join device-specific rooms for real-time updates
      socket.on("join_device", (deviceId) => {
        socket.join(`device_${deviceId}`);
        logger.info(`Socket ${socket.id} joined device room: ${deviceId}`);
      });

      // Leave device room
      socket.on("leave_device", (deviceId) => {
        socket.leave(`device_${deviceId}`);
        logger.info(`Socket ${socket.id} left device room: ${deviceId}`);
      });

      // Handle disconnection
      socket.on("disconnect", () => {
        logger.info(`Socket disconnected: ${socket.id}`);
      });

      // Handle errors
      socket.on("error", (error) => {
        logger.error(`Socket error for ${socket.id}:`, error);
      });
    });
  }

  /**
   * Initialize error handling
   */
  initializeErrorHandling() {
    // 404 handler for non-API routes
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: "Page not found",
        path: req.originalUrl,
      });
    });

    // Global error handler
    this.app.use(SecurityMiddleware.errorHandler);

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (err) => {
      logger.error("Unhandled Promise Rejection:", err);
      this.gracefulShutdown("UNHANDLED_REJECTION");
    });

    // Handle uncaught exceptions
    process.on("uncaughtException", (err) => {
      logger.error("Uncaught Exception:", err);
      this.gracefulShutdown("UNCAUGHT_EXCEPTION");
    });

    // Handle SIGTERM
    process.on("SIGTERM", () => {
      logger.info("SIGTERM received");
      this.gracefulShutdown("SIGTERM");
    });

    // Handle SIGINT
    process.on("SIGINT", () => {
      logger.info("SIGINT received");
      this.gracefulShutdown("SIGINT");
    });
  }

  /**
   * Start the server
   */
  async start() {
    try {
      // Connect to database
      await database.connect();

      // Start server
      this.server.listen(this.port, () => {
        logger.info(`🚀 AI GPS Tracker Server running on port ${this.port}`);
        logger.info(
          `📚 API Documentation: http://localhost:${this.port}/api-docs`
        );
        logger.info(`🏥 Health Check: http://localhost:${this.port}/health`);
        logger.info(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);
      });
    } catch (error) {
      logger.error("Failed to start server:", error);
      process.exit(1);
    }
  }

  /**
   * Graceful shutdown
   * @param {string} signal - Shutdown signal
   */
  async gracefulShutdown(signal) {
    logger.info(`Graceful shutdown initiated by ${signal}`);

    // Close server
    this.server.close(async () => {
      logger.info("HTTP server closed");

      // Close database connection
      await database.disconnect();

      // Exit process
      process.exit(signal === "UNCAUGHT_EXCEPTION" ? 1 : 0);
    });

    // Force close after 10 seconds
    setTimeout(() => {
      logger.error(
        "Could not close connections in time, forcefully shutting down"
      );
      process.exit(1);
    }, 10000);
  }
}

// Create and start server
const server = new GPSTrackerServer();

// Start server only if not in test environment
if (process.env.NODE_ENV !== "test") {
  server.start();
}

module.exports = server;
