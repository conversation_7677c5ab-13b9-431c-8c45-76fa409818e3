const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');
const config = require('config');

/**
 * API Gateway Service
 */
class APIGateway {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || config.get('services.gateway.port') || 3000;
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000,
      max: 1000,
      message: { error: 'Too many requests' }
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        service: 'api-gateway',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Service proxies (with fallback for development)
    const services = {
      '/api/auth': process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
      '/api/devices': process.env.DEVICE_SERVICE_URL || 'http://localhost:3002',
      '/api/locations': process.env.LOCATION_SERVICE_URL || 'http://localhost:3003',
      '/api/ai': process.env.AI_SERVICE_URL || 'http://localhost:3004',
      '/api/notifications': process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005'
    };

    Object.entries(services).forEach(([path, target]) => {
      this.app.use(path, createProxyMiddleware({
        target,
        changeOrigin: true,
        pathRewrite: { [`^${path}`]: '' },
        onError: (err, req, res) => {
          console.error(`Proxy error for ${path}:`, err.message);
          res.status(503).json({
            success: false,
            message: 'Service temporarily unavailable',
            service: path
          });
        }
      }));
    });

    // Default route
    this.app.get('/', (req, res) => {
      res.json({
        message: 'Enterprise AI GPS Tracker API Gateway',
        version: '2.0.0',
        services: Object.keys(services),
        documentation: '/api-docs'
      });
    });
  }

  setupErrorHandling() {
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: 'Endpoint not found',
        path: req.originalUrl
      });
    });

    this.app.use((error, req, res, next) => {
      console.error('Gateway error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    });
  }

  start() {
    this.server = this.app.listen(this.port, () => {
      console.log(`🚪 API Gateway running on port ${this.port}`);
    });
  }
}

const gateway = new APIGateway();
gateway.start();
