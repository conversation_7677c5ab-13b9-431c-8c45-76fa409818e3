const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { nanoid } = require('nanoid');

/**
 * Enterprise User Schema with Multi-tenant Support and Advanced Security
 */
const userSchema = new mongoose.Schema({
  // Primary Identification
  userId: {
    type: String,
    unique: true,
    default: () => `usr_${nanoid(12)}`,
    index: true
  },
  
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    lowercase: true,
    minlength: [3, 'Username must be at least 3 characters'],
    maxlength: [30, 'Username cannot exceed 30 characters'],
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'],
    index: true
  },
  
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email'],
    index: true
  },
  
  // Authentication
  password: {
    type: String,
    required: function() {
      return !this.oauth.google.id && !this.oauth.microsoft.id;
    },
    minlength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  
  // OAuth Integration
  oauth: {
    google: {
      id: String,
      email: String,
      verified: { type: Boolean, default: false }
    },
    microsoft: {
      id: String,
      email: String,
      verified: { type: Boolean, default: false }
    }
  },
  
  // Personal Information
  profile: {
    firstName: {
      type: String,
      required: [true, 'First name is required'],
      trim: true,
      maxlength: [50, 'First name cannot exceed 50 characters']
    },
    lastName: {
      type: String,
      required: [true, 'Last name is required'],
      trim: true,
      maxlength: [50, 'Last name cannot exceed 50 characters']
    },
    displayName: String,
    avatar: {
      url: String,
      publicId: String
    },
    phone: {
      number: String,
      verified: { type: Boolean, default: false },
      verificationCode: String,
      verificationExpires: Date
    },
    timezone: {
      type: String,
      default: 'UTC'
    },
    language: {
      type: String,
      default: 'en',
      enum: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko']
    }
  },
  
  // Multi-tenant Support
  tenants: [{
    tenantId: {
      type: String,
      required: true,
      index: true
    },
    role: {
      type: String,
      required: true,
      enum: ['admin', 'manager', 'operator', 'viewer']
    },
    permissions: [String],
    joinedAt: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  
  // Primary Role (for single-tenant or default)
  role: {
    type: String,
    enum: ['super_admin', 'admin', 'manager', 'operator', 'viewer', 'device'],
    default: 'viewer',
    index: true
  },
  
  // Account Status
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  
  isVerified: {
    type: Boolean,
    default: false
  },
  
  // Security Features
  security: {
    // Account Locking
    isLocked: {
      type: Boolean,
      default: false
    },
    lockUntil: Date,
    loginAttempts: {
      type: Number,
      default: 0
    },
    
    // Two-Factor Authentication
    twoFactorAuth: {
      enabled: { type: Boolean, default: false },
      secret: String,
      backupCodes: [String],
      lastUsed: Date
    },
    
    // Password Security
    passwordHistory: [{
      hash: String,
      createdAt: { type: Date, default: Date.now }
    }],
    passwordChangedAt: Date,
    passwordResetToken: String,
    passwordResetExpires: Date,
    
    // Session Management
    activeSessions: [{
      sessionId: String,
      deviceInfo: {
        userAgent: String,
        ip: String,
        location: String
      },
      createdAt: { type: Date, default: Date.now },
      lastActivity: { type: Date, default: Date.now },
      isActive: { type: Boolean, default: true }
    }],
    
    // API Keys
    apiKeys: [{
      keyId: String,
      name: String,
      keyHash: String,
      permissions: [String],
      lastUsed: Date,
      expiresAt: Date,
      isActive: { type: Boolean, default: true },
      createdAt: { type: Date, default: Date.now }
    }]
  },
  
  // Verification
  verification: {
    email: {
      token: String,
      expires: Date,
      verified: { type: Boolean, default: false }
    },
    phone: {
      code: String,
      expires: Date,
      verified: { type: Boolean, default: false }
    }
  },
  
  // Preferences
  preferences: {
    notifications: {
      email: {
        alerts: { type: Boolean, default: true },
        reports: { type: Boolean, default: true },
        marketing: { type: Boolean, default: false }
      },
      sms: {
        alerts: { type: Boolean, default: false },
        reports: { type: Boolean, default: false }
      },
      push: {
        alerts: { type: Boolean, default: true },
        reports: { type: Boolean, default: false }
      }
    },
    dashboard: {
      theme: { type: String, enum: ['light', 'dark', 'auto'], default: 'light' },
      layout: { type: String, enum: ['compact', 'comfortable'], default: 'comfortable' },
      defaultView: { type: String, enum: ['dashboard', 'map', 'devices'], default: 'dashboard' }
    },
    privacy: {
      shareLocation: { type: Boolean, default: false },
      shareAnalytics: { type: Boolean, default: true },
      allowTracking: { type: Boolean, default: true }
    }
  },
  
  // Subscription & Billing
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'professional', 'enterprise'],
      default: 'free'
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'cancelled', 'past_due'],
      default: 'active'
    },
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
    cancelAtPeriodEnd: { type: Boolean, default: false }
  },
  
  // Usage Statistics
  usage: {
    devicesCount: { type: Number, default: 0 },
    locationsCount: { type: Number, default: 0 },
    apiCallsCount: { type: Number, default: 0 },
    storageUsed: { type: Number, default: 0 }, // in bytes
    lastLogin: Date,
    lastActivity: Date,
    totalLogins: { type: Number, default: 0 }
  },
  
  // Compliance & Audit
  compliance: {
    gdprConsent: {
      given: { type: Boolean, default: false },
      date: Date,
      version: String
    },
    dataRetention: {
      deleteAfter: Date,
      reason: String
    },
    auditLog: [{
      action: String,
      timestamp: { type: Date, default: Date.now },
      ip: String,
      userAgent: String,
      details: mongoose.Schema.Types.Mixed
    }]
  },
  
  // Metadata
  metadata: {
    source: String, // registration source
    referrer: String,
    utm: {
      source: String,
      medium: String,
      campaign: String
    },
    tags: [String],
    notes: String
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.security.passwordHistory;
      delete ret.security.twoFactorAuth.secret;
      delete ret.security.apiKeys;
      delete ret.verification;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Compound Indexes for Performance
userSchema.index({ email: 1, isActive: 1 });
userSchema.index({ username: 1, isActive: 1 });
userSchema.index({ 'tenants.tenantId': 1, 'tenants.isActive': 1 });
userSchema.index({ role: 1, isActive: 1 });
userSchema.index({ 'subscription.plan': 1, 'subscription.status': 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ 'usage.lastActivity': -1 });

// Text Index for Search
userSchema.index({
  username: 'text',
  email: 'text',
  'profile.firstName': 'text',
  'profile.lastName': 'text'
});

// TTL Index for Inactive Users (optional)
userSchema.index({ 
  'usage.lastActivity': 1 
}, { 
  expireAfterSeconds: 31536000, // 1 year
  partialFilterExpression: { 
    isActive: false,
    'subscription.plan': 'free'
  }
});

// Virtual Properties
userSchema.virtual('fullName').get(function() {
  return `${this.profile.firstName} ${this.profile.lastName}`;
});

userSchema.virtual('isLocked').get(function() {
  return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

userSchema.virtual('deviceLimit').get(function() {
  const limits = {
    free: 1,
    basic: 5,
    professional: 25,
    enterprise: 1000
  };
  return limits[this.subscription.plan] || 1;
});

// Pre-save Middleware
userSchema.pre('save', async function(next) {
  // Hash password if modified
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 12);
    this.security.passwordChangedAt = new Date();
  }
  
  // Generate display name if not provided
  if (!this.profile.displayName) {
    this.profile.displayName = this.fullName;
  }
  
  // Update last activity
  if (this.isNew) {
    this.usage.lastActivity = new Date();
  }
  
  next();
});

// Instance Methods
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateAuthToken = function() {
  const jwt = require('jsonwebtoken');
  const config = require('config');
  const authConfig = config.get('auth.jwt');
  
  const payload = {
    sub: this._id,
    username: this.username,
    email: this.email,
    role: this.role,
    tenants: this.tenants.filter(t => t.isActive).map(t => t.tenantId),
    iat: Math.floor(Date.now() / 1000),
    jti: require('uuid').v4()
  };
  
  return jwt.sign(payload, authConfig.secret, {
    expiresIn: authConfig.expiresIn,
    issuer: authConfig.issuer,
    audience: authConfig.audience
  });
};

userSchema.methods.generateRefreshToken = function() {
  const jwt = require('jsonwebtoken');
  const config = require('config');
  const authConfig = config.get('auth.jwt');
  
  const payload = {
    sub: this._id,
    type: 'refresh',
    iat: Math.floor(Date.now() / 1000),
    jti: require('uuid').v4()
  };
  
  return jwt.sign(payload, authConfig.refreshSecret, {
    expiresIn: authConfig.refreshExpiresIn,
    issuer: authConfig.issuer,
    audience: authConfig.audience
  });
};

userSchema.methods.generateApiKey = async function(name, permissions = []) {
  const keyId = `ak_${nanoid(16)}`;
  const key = `${keyId}.${nanoid(32)}`;
  const keyHash = crypto.createHash('sha256').update(key).digest('hex');
  
  this.security.apiKeys.push({
    keyId,
    name,
    keyHash,
    permissions,
    createdAt: new Date()
  });
  
  await this.save();
  return key;
};

userSchema.methods.hasPermission = function(permission, tenantId = null) {
  if (this.role === 'super_admin') return true;
  
  if (tenantId) {
    const tenant = this.tenants.find(t => t.tenantId === tenantId && t.isActive);
    if (!tenant) return false;
    return tenant.permissions.includes(permission) || tenant.permissions.includes('*');
  }
  
  const config = require('config');
  const rbacConfig = config.get('auth.rbac');
  const rolePermissions = rbacConfig.permissions[this.role] || [];
  
  return rolePermissions.includes(permission) || rolePermissions.includes('*');
};

userSchema.methods.addToTenant = function(tenantId, role, permissions = []) {
  const existingTenant = this.tenants.find(t => t.tenantId === tenantId);
  
  if (existingTenant) {
    existingTenant.role = role;
    existingTenant.permissions = permissions;
    existingTenant.isActive = true;
  } else {
    this.tenants.push({
      tenantId,
      role,
      permissions,
      joinedAt: new Date()
    });
  }
  
  return this.save();
};

// Static Methods
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase(), isActive: true });
};

userSchema.statics.findByUsername = function(username) {
  return this.findOne({ username: username.toLowerCase(), isActive: true });
};

userSchema.statics.handleLoginAttempt = async function(userId, success) {
  const maxAttempts = 5;
  const lockTime = 2 * 60 * 60 * 1000; // 2 hours
  
  if (!userId) return;
  
  const user = await this.findById(userId);
  if (!user) return;
  
  if (success) {
    // Reset login attempts on successful login
    if (user.security.loginAttempts > 0) {
      await this.updateOne(
        { _id: userId },
        {
          $unset: { 'security.loginAttempts': 1, 'security.lockUntil': 1 },
          $set: { 'usage.lastLogin': new Date() },
          $inc: { 'usage.totalLogins': 1 }
        }
      );
    }
  } else {
    // Increment login attempts
    const updates = { $inc: { 'security.loginAttempts': 1 } };
    
    // Lock account if max attempts reached
    if (user.security.loginAttempts + 1 >= maxAttempts && !user.isLocked) {
      updates.$set = { 'security.lockUntil': Date.now() + lockTime };
    }
    
    await this.updateOne({ _id: userId }, updates);
  }
};

module.exports = mongoose.model('User', userSchema);
