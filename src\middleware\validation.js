const { body, param, query, validationResult } = require('express-validator');
const logger = require('../utils/logger');

/**
 * Validation Middleware using express-validator
 */
class ValidationMiddleware {
  /**
   * Handle validation errors
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static handleValidationErrors(req, res, next) {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      const formattedErrors = errors.array().map(error => ({
        field: error.path || error.param,
        message: error.msg,
        value: error.value
      }));
      
      logger.warn('Validation errors:', {
        url: req.originalUrl,
        method: req.method,
        errors: formattedErrors,
        ip: req.ip
      });
      
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: formattedErrors
      });
    }
    
    next();
  }
  
  /**
   * User registration validation rules
   * @returns {Array} Validation rules
   */
  static validateUserRegistration() {
    return [
      body('username')
        .isLength({ min: 3, max: 30 })
        .withMessage('Username must be between 3 and 30 characters')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Username can only contain letters, numbers, and underscores'),
      
      body('email')
        .isEmail()
        .withMessage('Please provide a valid email address')
        .normalizeEmail(),
      
      body('password')
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
      
      body('firstName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name is required and cannot exceed 50 characters'),
      
      body('lastName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name is required and cannot exceed 50 characters'),
      
      body('phone')
        .optional()
        .matches(/^\+?[\d\s-()]+$/)
        .withMessage('Please provide a valid phone number'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * User login validation rules
   * @returns {Array} Validation rules
   */
  static validateUserLogin() {
    return [
      body('email')
        .isEmail()
        .withMessage('Please provide a valid email address')
        .normalizeEmail(),
      
      body('password')
        .notEmpty()
        .withMessage('Password is required'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * Device registration validation rules
   * @returns {Array} Validation rules
   */
  static validateDeviceRegistration() {
    return [
      body('deviceId')
        .isLength({ min: 8, max: 20 })
        .withMessage('Device ID must be between 8 and 20 characters')
        .matches(/^[A-Z0-9]+$/)
        .withMessage('Device ID must contain only uppercase letters and numbers'),
      
      body('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Device name is required and cannot exceed 100 characters'),
      
      body('type')
        .isIn(['vehicle', 'person', 'asset', 'pet', 'other'])
        .withMessage('Invalid device type'),
      
      body('description')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Description cannot exceed 500 characters'),
      
      body('model')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Model cannot exceed 100 characters'),
      
      body('manufacturer')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Manufacturer cannot exceed 100 characters'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * Location update validation rules
   * @returns {Array} Validation rules
   */
  static validateLocationUpdate() {
    return [
      body('latitude')
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),
      
      body('longitude')
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),
      
      body('accuracy')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Accuracy must be a positive number'),
      
      body('altitude')
        .optional()
        .isFloat()
        .withMessage('Altitude must be a number'),
      
      body('speed')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Speed must be a positive number'),
      
      body('heading')
        .optional()
        .isFloat({ min: 0, max: 360 })
        .withMessage('Heading must be between 0 and 360'),
      
      body('batteryLevel')
        .optional()
        .isInt({ min: 0, max: 100 })
        .withMessage('Battery level must be between 0 and 100'),
      
      body('timestamp')
        .optional()
        .isISO8601()
        .withMessage('Timestamp must be a valid ISO 8601 date'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * Geofence creation validation rules
   * @returns {Array} Validation rules
   */
  static validateGeofenceCreation() {
    return [
      body('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Geofence name is required and cannot exceed 100 characters'),
      
      body('type')
        .isIn(['circle', 'polygon', 'rectangle'])
        .withMessage('Invalid geofence type'),
      
      body('description')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Description cannot exceed 500 characters'),
      
      // Circle validation
      body('center.latitude')
        .if(body('type').equals('circle'))
        .isFloat({ min: -90, max: 90 })
        .withMessage('Center latitude must be between -90 and 90'),
      
      body('center.longitude')
        .if(body('type').equals('circle'))
        .isFloat({ min: -180, max: 180 })
        .withMessage('Center longitude must be between -180 and 180'),
      
      body('radius')
        .if(body('type').equals('circle'))
        .isFloat({ min: 1, max: 100000 })
        .withMessage('Radius must be between 1 and 100000 meters'),
      
      // Polygon validation
      body('coordinates')
        .if(body('type').isIn(['polygon', 'rectangle']))
        .isArray({ min: 3 })
        .withMessage('Polygon must have at least 3 coordinates'),
      
      body('coordinates.*.latitude')
        .if(body('type').isIn(['polygon', 'rectangle']))
        .isFloat({ min: -90, max: 90 })
        .withMessage('All latitudes must be between -90 and 90'),
      
      body('coordinates.*.longitude')
        .if(body('type').isIn(['polygon', 'rectangle']))
        .isFloat({ min: -180, max: 180 })
        .withMessage('All longitudes must be between -180 and 180'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * Query parameter validation for location history
   * @returns {Array} Validation rules
   */
  static validateLocationQuery() {
    return [
      query('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),
      
      query('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be a valid ISO 8601 date'),
      
      query('limit')
        .optional()
        .isInt({ min: 1, max: 1000 })
        .withMessage('Limit must be between 1 and 1000'),
      
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * MongoDB ObjectId validation
   * @param {string} field - Field name
   * @returns {Array} Validation rules
   */
  static validateObjectId(field = 'id') {
    return [
      param(field)
        .isMongoId()
        .withMessage(`Invalid ${field} format`),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * Pagination validation
   * @returns {Array} Validation rules
   */
  static validatePagination() {
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer')
        .toInt(),
      
      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100')
        .toInt(),
      
      query('sort')
        .optional()
        .isIn(['createdAt', '-createdAt', 'updatedAt', '-updatedAt', 'name', '-name'])
        .withMessage('Invalid sort field'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * Alert creation validation
   * @returns {Array} Validation rules
   */
  static validateAlertCreation() {
    return [
      body('type')
        .isIn([
          'geofence_enter', 'geofence_exit', 'geofence_dwell',
          'speed_limit', 'low_battery', 'device_offline',
          'panic_button', 'route_deviation', 'anomaly_detected',
          'maintenance_due', 'custom'
        ])
        .withMessage('Invalid alert type'),
      
      body('severity')
        .isIn(['info', 'warning', 'critical', 'emergency'])
        .withMessage('Invalid severity level'),
      
      body('title')
        .trim()
        .isLength({ min: 1, max: 200 })
        .withMessage('Title is required and cannot exceed 200 characters'),
      
      body('message')
        .trim()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Message is required and cannot exceed 1000 characters'),
      
      body('deviceId')
        .isMongoId()
        .withMessage('Invalid device ID'),
      
      this.handleValidationErrors
    ];
  }
  
  /**
   * Custom validation for coordinate arrays
   * @param {Array} coordinates - Array of coordinates
   * @returns {boolean} Validation result
   */
  static validateCoordinates(coordinates) {
    if (!Array.isArray(coordinates)) return false;
    
    return coordinates.every(coord => 
      Array.isArray(coord) && 
      coord.length === 2 && 
      typeof coord[0] === 'number' && 
      typeof coord[1] === 'number' &&
      coord[0] >= -180 && coord[0] <= 180 && // longitude
      coord[1] >= -90 && coord[1] <= 90      // latitude
    );
  }
}

module.exports = ValidationMiddleware;
