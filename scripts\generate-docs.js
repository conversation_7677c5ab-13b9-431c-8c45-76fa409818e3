#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const swaggerJSDoc = require('swagger-jsdoc');
const glob = require('glob');

/**
 * Documentation generation script
 */
class DocumentationGenerator {
  constructor() {
    this.options = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'Enterprise AI GPS Tracker API',
          version: '2.0.0',
          description: 'Production-level AI-powered GPS tracking system with microservices architecture',
          contact: {
            name: 'API Support',
            email: '<EMAIL>'
          },
          license: {
            name: 'MIT',
            url: 'https://opensource.org/licenses/MIT'
          }
        },
        servers: [
          {
            url: 'http://localhost:3000',
            description: 'Development server'
          },
          {
            url: 'https://api.gps-tracker.com',
            description: 'Production server'
          }
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT'
            },
            apiKeyAuth: {
              type: 'apiKey',
              in: 'header',
              name: 'X-API-Key'
            }
          }
        },
        security: [
          {
            bearerAuth: []
          }
        ]
      },
      apis: [] // Will be populated dynamically
    };
  }

  async findApiFiles() {
    console.log('🔍 Finding API files...');
    
    const patterns = [
      'services/**/*.js',
      'shared/**/*.js',
      '!node_modules/**',
      '!tests/**',
      '!coverage/**'
    ];

    const files = [];
    
    for (const pattern of patterns) {
      const matches = glob.sync(pattern);
      files.push(...matches);
    }

    console.log(`  ✅ Found ${files.length} API files`);
    return files;
  }

  async generateSwaggerSpec() {
    console.log('📝 Generating Swagger specification...');
    
    const apiFiles = await this.findApiFiles();
    this.options.apis = apiFiles;

    try {
      const specs = swaggerJSDoc(this.options);
      
      // Ensure docs directory exists
      await fs.ensureDir('docs');
      
      // Write swagger.json
      await fs.writeJson('docs/swagger.json', specs, { spaces: 2 });
      console.log('  ✅ Generated docs/swagger.json');
      
      // Write swagger.yaml
      const yaml = require('js-yaml');
      await fs.writeFile('docs/swagger.yaml', yaml.dump(specs));
      console.log('  ✅ Generated docs/swagger.yaml');
      
      return specs;
      
    } catch (error) {
      console.error('  ❌ Failed to generate Swagger spec:', error.message);
      throw error;
    }
  }

  async generateMarkdownDocs(specs) {
    console.log('📚 Generating Markdown documentation...');
    
    try {
      let markdown = `# Enterprise AI GPS Tracker API Documentation\n\n`;
      markdown += `Version: ${specs.info.version}\n\n`;
      markdown += `${specs.info.description}\n\n`;
      
      // Add servers
      markdown += `## Servers\n\n`;
      specs.servers.forEach(server => {
        markdown += `- **${server.description}**: ${server.url}\n`;
      });
      markdown += '\n';
      
      // Add authentication
      markdown += `## Authentication\n\n`;
      markdown += `This API uses JWT Bearer tokens for authentication.\n\n`;
      markdown += `Include the token in the Authorization header:\n`;
      markdown += `\`\`\`\nAuthorization: Bearer <your-jwt-token>\n\`\`\`\n\n`;
      
      // Add endpoints
      if (specs.paths) {
        markdown += `## Endpoints\n\n`;
        
        Object.keys(specs.paths).forEach(path => {
          const pathObj = specs.paths[path];
          markdown += `### ${path}\n\n`;
          
          Object.keys(pathObj).forEach(method => {
            const operation = pathObj[method];
            markdown += `#### ${method.toUpperCase()}\n\n`;
            
            if (operation.summary) {
              markdown += `**Summary**: ${operation.summary}\n\n`;
            }
            
            if (operation.description) {
              markdown += `**Description**: ${operation.description}\n\n`;
            }
            
            if (operation.parameters) {
              markdown += `**Parameters**:\n`;
              operation.parameters.forEach(param => {
                markdown += `- \`${param.name}\` (${param.in}): ${param.description || 'No description'}\n`;
              });
              markdown += '\n';
            }
            
            markdown += '---\n\n';
          });
        });
      }
      
      await fs.writeFile('docs/API.md', markdown);
      console.log('  ✅ Generated docs/API.md');
      
    } catch (error) {
      console.error('  ❌ Failed to generate Markdown docs:', error.message);
      throw error;
    }
  }

  async generatePostmanCollection(specs) {
    console.log('📮 Generating Postman collection...');
    
    try {
      const collection = {
        info: {
          name: specs.info.title,
          description: specs.info.description,
          version: specs.info.version,
          schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
        },
        auth: {
          type: 'bearer',
          bearer: [
            {
              key: 'token',
              value: '{{jwt_token}}',
              type: 'string'
            }
          ]
        },
        variable: [
          {
            key: 'base_url',
            value: 'http://localhost:3000',
            type: 'string'
          },
          {
            key: 'jwt_token',
            value: '',
            type: 'string'
          }
        ],
        item: []
      };

      if (specs.paths) {
        Object.keys(specs.paths).forEach(path => {
          const pathObj = specs.paths[path];
          
          Object.keys(pathObj).forEach(method => {
            const operation = pathObj[method];
            
            const item = {
              name: operation.summary || `${method.toUpperCase()} ${path}`,
              request: {
                method: method.toUpperCase(),
                header: [
                  {
                    key: 'Content-Type',
                    value: 'application/json'
                  }
                ],
                url: {
                  raw: `{{base_url}}${path}`,
                  host: ['{{base_url}}'],
                  path: path.split('/').filter(p => p)
                }
              }
            };
            
            if (operation.requestBody) {
              item.request.body = {
                mode: 'raw',
                raw: JSON.stringify({}, null, 2)
              };
            }
            
            collection.item.push(item);
          });
        });
      }

      await fs.writeJson('docs/postman-collection.json', collection, { spaces: 2 });
      console.log('  ✅ Generated docs/postman-collection.json');
      
    } catch (error) {
      console.error('  ❌ Failed to generate Postman collection:', error.message);
      throw error;
    }
  }

  async generate() {
    console.log('📖 Starting documentation generation...\n');

    try {
      const specs = await this.generateSwaggerSpec();
      await this.generateMarkdownDocs(specs);
      await this.generatePostmanCollection(specs);

      console.log('\n🎉 Documentation generation completed successfully!');
      console.log('\nGenerated files:');
      console.log('  📄 docs/swagger.json');
      console.log('  📄 docs/swagger.yaml');
      console.log('  📄 docs/API.md');
      console.log('  📄 docs/postman-collection.json');
      
    } catch (error) {
      console.error('❌ Documentation generation failed:', error.message);
      process.exit(1);
    }
  }
}

// Add js-yaml dependency check
try {
  require('js-yaml');
} catch (error) {
  console.warn('⚠️  js-yaml not found, YAML generation will be skipped');
}

// Run documentation generation
const generator = new DocumentationGenerator();
generator.generate().catch(error => {
  console.error('Documentation generation failed:', error);
  process.exit(1);
});
