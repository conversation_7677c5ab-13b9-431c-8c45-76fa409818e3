const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');

/**
 * Location Tracking Service
 */
class LocationService {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3003;
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(compression());
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        service: 'location-service',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Location routes
    this.app.post('/update', (req, res) => {
      res.json({
        success: true,
        message: 'Location updated successfully',
        timestamp: new Date().toISOString()
      });
    });

    this.app.get('/devices/:deviceId/history', (req, res) => {
      res.json({
        success: true,
        message: 'Location history retrieved',
        data: []
      });
    });
  }

  setupErrorHandling() {
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: 'Endpoint not found'
      });
    });

    this.app.use((error, req, res, next) => {
      console.error('Location service error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    });
  }

  start() {
    this.server = this.app.listen(this.port, () => {
      console.log(`📍 Location Service running on port ${this.port}`);
    });
  }
}

const service = new LocationService();
service.start();
