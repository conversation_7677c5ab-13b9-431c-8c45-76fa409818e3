const Device = require('../models/Device');
const Location = require('../models/Location');
const logger = require('../utils/logger');

/**
 * Device Controller
 */
class DeviceController {
  /**
   * Register a new device
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async registerDevice(req, res) {
    try {
      const { deviceId, name, type, description, model, manufacturer, serialNumber } = req.body;
      const owner = req.user._id;
      
      // Check if device already exists
      const existingDevice = await Device.findOne({ deviceId });
      
      if (existingDevice) {
        return res.status(400).json({
          success: false,
          message: 'Device ID already registered'
        });
      }
      
      // Create new device
      const device = new Device({
        deviceId,
        name,
        type,
        description,
        model,
        manufacturer,
        serialNumber,
        owner
      });
      
      await device.save();
      
      logger.info(`New device registered: ${deviceId} by user: ${req.user.email}`);
      
      res.status(201).json({
        success: true,
        message: 'Device registered successfully',
        data: { device }
      });
      
    } catch (error) {
      logger.error('Device registration error:', error);
      
      if (error.code === 11000) {
        return res.status(400).json({
          success: false,
          message: 'Device ID already exists'
        });
      }
      
      res.status(500).json({
        success: false,
        message: 'Device registration failed'
      });
    }
  }
  
  /**
   * Get all devices for the authenticated user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getDevices(req, res) {
    try {
      const { page = 1, limit = 10, type, status } = req.query;
      const owner = req.user._id;
      
      // Build query
      const query = { owner };
      
      if (type) {
        query.type = type;
      }
      
      if (status === 'active') {
        query.isActive = true;
      } else if (status === 'inactive') {
        query.isActive = false;
      }
      
      if (status === 'online') {
        query.isOnline = true;
      } else if (status === 'offline') {
        query.isOnline = false;
      }
      
      // Execute query with pagination
      const devices = await Device.find(query)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .populate('owner', 'username email firstName lastName');
      
      const total = await Device.countDocuments(query);
      
      res.json({
        success: true,
        data: {
          devices,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
      
    } catch (error) {
      logger.error('Get devices error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get devices'
      });
    }
  }
  
  /**
   * Get a specific device by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getDevice(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;
      
      const device = await Device.findOne({
        _id: id,
        owner: userId
      }).populate('owner', 'username email firstName lastName');
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      // Get latest location
      const latestLocation = await Location.findOne({ device: id })
        .sort({ timestamp: -1 })
        .limit(1);
      
      res.json({
        success: true,
        data: {
          device,
          latestLocation
        }
      });
      
    } catch (error) {
      logger.error('Get device error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get device'
      });
    }
  }
  
  /**
   * Update device information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateDevice(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;
      const updates = req.body;
      
      // Remove fields that shouldn't be updated
      delete updates.deviceId;
      delete updates.owner;
      delete updates.lastLocation;
      delete updates.lastSeen;
      delete updates.stats;
      
      const device = await Device.findOneAndUpdate(
        { _id: id, owner: userId },
        { $set: updates },
        { new: true, runValidators: true }
      );
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      logger.info(`Device updated: ${device.deviceId} by user: ${req.user.email}`);
      
      res.json({
        success: true,
        message: 'Device updated successfully',
        data: { device }
      });
      
    } catch (error) {
      logger.error('Update device error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update device'
      });
    }
  }
  
  /**
   * Delete a device
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteDevice(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;
      
      const device = await Device.findOneAndDelete({
        _id: id,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      // Optionally delete associated location data
      // await Location.deleteMany({ device: id });
      
      logger.info(`Device deleted: ${device.deviceId} by user: ${req.user.email}`);
      
      res.json({
        success: true,
        message: 'Device deleted successfully'
      });
      
    } catch (error) {
      logger.error('Delete device error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete device'
      });
    }
  }
  
  /**
   * Get device statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getDeviceStats(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;
      const { days = 7 } = req.query;
      
      const device = await Device.findOne({
        _id: id,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(days));
      
      // Get location statistics
      const locationStats = await Location.aggregate([
        {
          $match: {
            device: device._id,
            timestamp: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: null,
            totalLocations: { $sum: 1 },
            totalDistance: { $sum: '$movement.distance' },
            averageSpeed: { $avg: '$speed' },
            maxSpeed: { $max: '$speed' },
            averageAccuracy: { $avg: '$accuracy' }
          }
        }
      ]);
      
      const stats = locationStats[0] || {
        totalLocations: 0,
        totalDistance: 0,
        averageSpeed: 0,
        maxSpeed: 0,
        averageAccuracy: 0
      };
      
      // Convert distance from meters to kilometers
      stats.totalDistance = (stats.totalDistance / 1000).toFixed(2);
      
      res.json({
        success: true,
        data: {
          device: {
            id: device._id,
            name: device.name,
            deviceId: device.deviceId,
            type: device.type
          },
          period: `${days} days`,
          statistics: stats
        }
      });
      
    } catch (error) {
      logger.error('Get device stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get device statistics'
      });
    }
  }
  
  /**
   * Update device status (online/offline)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateDeviceStatus(req, res) {
    try {
      const { id } = req.params;
      const { isOnline, batteryLevel, signalStrength } = req.body;
      const userId = req.user._id;
      
      const updateData = {
        lastSeen: new Date()
      };
      
      if (typeof isOnline === 'boolean') {
        updateData.isOnline = isOnline;
      }
      
      if (typeof batteryLevel === 'number') {
        updateData.batteryLevel = batteryLevel;
      }
      
      if (typeof signalStrength === 'number') {
        updateData.signalStrength = signalStrength;
      }
      
      const device = await Device.findOneAndUpdate(
        { _id: id, owner: userId },
        { $set: updateData },
        { new: true }
      );
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Device status updated successfully',
        data: { device }
      });
      
    } catch (error) {
      logger.error('Update device status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update device status'
      });
    }
  }
}

module.exports = DeviceController;
