const mongoose = require('mongoose');

/**
 * Location Schema for GPS Tracking History
 */
const locationSchema = new mongoose.Schema({
  // Device Reference
  device: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device',
    required: [true, 'Device reference is required'],
    index: true
  },
  
  // Geographic Information
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true,
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: [true, 'Coordinates are required'],
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates format [longitude, latitude]'
      }
    }
  },
  
  // Address Information
  address: {
    formatted: String,
    street: String,
    city: String,
    state: String,
    country: String,
    postalCode: String
  },
  
  // Accuracy and Quality
  accuracy: {
    type: Number,
    min: [0, 'Accuracy cannot be negative'],
    default: null // in meters
  },
  
  altitude: {
    type: Number,
    default: null // in meters
  },
  
  altitudeAccuracy: {
    type: Number,
    min: [0, 'Altitude accuracy cannot be negative'],
    default: null // in meters
  },
  
  // Movement Information
  speed: {
    type: Number,
    min: [0, 'Speed cannot be negative'],
    default: null // in km/h
  },
  
  heading: {
    type: Number,
    min: [0, 'Heading must be between 0 and 360'],
    max: [360, 'Heading must be between 0 and 360'],
    default: null // in degrees (0-360)
  },
  
  // Device Status at Time of Recording
  batteryLevel: {
    type: Number,
    min: [0, 'Battery level cannot be negative'],
    max: [100, 'Battery level cannot exceed 100'],
    default: null
  },
  
  signalStrength: {
    type: Number,
    min: [-120, 'Signal strength too low'],
    max: [0, 'Signal strength too high'],
    default: null // in dBm
  },
  
  // Data Source and Quality
  source: {
    type: String,
    enum: ['gps', 'network', 'passive', 'fused'],
    default: 'gps'
  },
  
  satellites: {
    type: Number,
    min: [0, 'Satellite count cannot be negative'],
    default: null
  },
  
  // Timing Information
  timestamp: {
    type: Date,
    required: [true, 'Timestamp is required'],
    index: true
  },
  
  serverTimestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // Movement Analysis
  movement: {
    isMoving: { type: Boolean, default: false },
    distance: { type: Number, default: 0 }, // distance from previous point in meters
    duration: { type: Number, default: 0 }, // time from previous point in seconds
    averageSpeed: { type: Number, default: 0 } // calculated average speed in km/h
  },
  
  // Geofence Information
  geofences: [{
    geofence: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Geofence'
    },
    event: {
      type: String,
      enum: ['enter', 'exit', 'inside']
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Additional Metadata
  metadata: {
    provider: String, // GPS provider information
    mock: { type: Boolean, default: false }, // if location is mocked
    indoor: { type: Boolean, default: false }, // if location is indoor
    confidence: { type: Number, min: 0, max: 1 }, // confidence level (0-1)
    rawData: mongoose.Schema.Types.Mixed // store raw GPS data if needed
  },
  
  // Data Processing Flags
  processed: {
    type: Boolean,
    default: false,
    index: true
  },
  
  anomaly: {
    detected: { type: Boolean, default: false },
    score: { type: Number, min: 0, max: 1 },
    reasons: [String]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance
locationSchema.index({ device: 1, timestamp: -1 });
locationSchema.index({ device: 1, serverTimestamp: -1 });
locationSchema.index({ location: '2dsphere' });
locationSchema.index({ timestamp: -1 });
locationSchema.index({ processed: 1, timestamp: -1 });
locationSchema.index({ 'anomaly.detected': 1 });

// TTL index for automatic cleanup (optional - remove old data after 1 year)
locationSchema.index({ serverTimestamp: 1 }, { expireAfterSeconds: 31536000 });

// Virtual for longitude
locationSchema.virtual('longitude').get(function() {
  return this.location.coordinates[0];
});

// Virtual for latitude
locationSchema.virtual('latitude').get(function() {
  return this.location.coordinates[1];
});

// Virtual for coordinate pair
locationSchema.virtual('coordinates').get(function() {
  return {
    longitude: this.location.coordinates[0],
    latitude: this.location.coordinates[1]
  };
});

// Pre-save middleware to calculate movement data
locationSchema.pre('save', async function(next) {
  if (this.isNew) {
    // Find the previous location for this device
    const previousLocation = await this.constructor
      .findOne({ device: this.device })
      .sort({ timestamp: -1 })
      .limit(1);
    
    if (previousLocation) {
      // Calculate distance using Haversine formula
      const distance = this.calculateDistance(
        previousLocation.location.coordinates[1],
        previousLocation.location.coordinates[0],
        this.location.coordinates[1],
        this.location.coordinates[0]
      );
      
      // Calculate time difference in seconds
      const timeDiff = (this.timestamp - previousLocation.timestamp) / 1000;
      
      // Update movement data
      this.movement.distance = distance;
      this.movement.duration = timeDiff;
      
      if (timeDiff > 0) {
        this.movement.averageSpeed = (distance / 1000) / (timeDiff / 3600); // km/h
        this.movement.isMoving = this.movement.averageSpeed > 1; // moving if > 1 km/h
      }
    }
  }
  
  next();
});

// Instance method to calculate distance between two points
locationSchema.methods.calculateDistance = function(lat1, lon1, lat2, lon2) {
  const R = 6371000; // Earth's radius in meters
  const dLat = this.toRadians(lat2 - lat1);
  const dLon = this.toRadians(lon2 - lon1);
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in meters
};

// Helper method to convert degrees to radians
locationSchema.methods.toRadians = function(degrees) {
  return degrees * (Math.PI / 180);
};

// Static method to find locations within a time range
locationSchema.statics.findByTimeRange = function(deviceId, startTime, endTime) {
  return this.find({
    device: deviceId,
    timestamp: {
      $gte: startTime,
      $lte: endTime
    }
  }).sort({ timestamp: 1 });
};

// Static method to find locations within a geographic area
locationSchema.statics.findInArea = function(deviceId, center, radius) {
  return this.find({
    device: deviceId,
    location: {
      $geoWithin: {
        $centerSphere: [center, radius / 6371000] // radius in radians
      }
    }
  }).sort({ timestamp: -1 });
};

// Static method to get latest location for a device
locationSchema.statics.getLatest = function(deviceId) {
  return this.findOne({ device: deviceId })
    .sort({ timestamp: -1 })
    .populate('device', 'name deviceId type');
};

module.exports = mongoose.model('Location', locationSchema);
