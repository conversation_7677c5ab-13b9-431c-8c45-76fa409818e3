const mongoose = require('mongoose');
const { nanoid } = require('nanoid');
const crypto = require('crypto');

/**
 * Enterprise Device Schema with Advanced Tracking and IoT Features
 */
const deviceSchema = new mongoose.Schema({
  // Primary Identification
  deviceId: {
    type: String,
    required: [true, 'Device ID is required'],
    unique: true,
    uppercase: true,
    trim: true,
    minlength: [8, 'Device ID must be at least 8 characters'],
    maxlength: [20, 'Device ID cannot exceed 20 characters'],
    match: [/^[A-Z0-9]+$/, 'Device ID must contain only uppercase letters and numbers'],
    index: true
  },
  
  internalId: {
    type: String,
    unique: true,
    default: () => `dev_${nanoid(12)}`,
    index: true
  },
  
  // Ownership & Tenant
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Device owner is required'],
    index: true
  },
  
  tenantId: {
    type: String,
    index: true
  },
  
  // Basic Information
  name: {
    type: String,
    required: [true, 'Device name is required'],
    trim: true,
    maxlength: [100, 'Device name cannot exceed 100 characters'],
    index: true
  },
  
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  type: {
    type: String,
    required: [true, 'Device type is required'],
    enum: {
      values: ['vehicle', 'person', 'asset', 'pet', 'drone', 'vessel', 'equipment', 'container', 'other'],
      message: 'Invalid device type'
    },
    index: true
  },
  
  category: {
    type: String,
    enum: ['personal', 'commercial', 'fleet', 'logistics', 'security', 'emergency'],
    default: 'personal',
    index: true
  },
  
  // Hardware Information
  hardware: {
    manufacturer: String,
    model: String,
    serialNumber: {
      type: String,
      index: { sparse: true }
    },
    firmwareVersion: String,
    hardwareVersion: String,
    imei: {
      type: String,
      index: { sparse: true },
      match: [/^\d{15}$/, 'IMEI must be 15 digits']
    },
    simCard: {
      iccid: String,
      carrier: String,
      plan: String
    },
    sensors: [{
      type: {
        type: String,
        enum: ['gps', 'accelerometer', 'gyroscope', 'magnetometer', 'temperature', 'humidity', 'pressure', 'camera', 'microphone']
      },
      model: String,
      accuracy: String,
      enabled: { type: Boolean, default: true }
    }]
  },
  
  // Status & Health
  status: {
    isActive: {
      type: Boolean,
      default: true,
      index: true
    },
    isOnline: {
      type: Boolean,
      default: false,
      index: true
    },
    lastSeen: {
      type: Date,
      index: true
    },
    lastHeartbeat: Date,
    connectionType: {
      type: String,
      enum: ['cellular', 'wifi', 'satellite', 'bluetooth', 'lora', 'unknown'],
      default: 'unknown'
    },
    signalStrength: {
      type: Number,
      min: -120,
      max: 0
    },
    batteryLevel: {
      type: Number,
      min: 0,
      max: 100,
      index: true
    },
    batteryVoltage: Number,
    isCharging: Boolean,
    temperature: Number,
    health: {
      score: {
        type: Number,
        min: 0,
        max: 100,
        default: 100
      },
      issues: [String],
      lastCheck: Date
    }
  },
  
  // Location & Movement
  location: {
    current: {
      type: {
        type: String,
        enum: ['Point'],
        default: 'Point'
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        index: '2dsphere'
      },
      accuracy: Number,
      altitude: Number,
      heading: Number,
      speed: Number,
      timestamp: Date,
      address: {
        formatted: String,
        street: String,
        city: String,
        state: String,
        country: String,
        postalCode: String
      }
    },
    home: {
      type: {
        type: String,
        enum: ['Point'],
        default: 'Point'
      },
      coordinates: [Number],
      radius: { type: Number, default: 100 }, // meters
      name: String
    },
    geofences: [{
      geofenceId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Geofence'
      },
      status: {
        type: String,
        enum: ['inside', 'outside', 'unknown'],
        default: 'unknown'
      },
      enteredAt: Date,
      exitedAt: Date
    }]
  },
  
  // Configuration
  configuration: {
    trackingInterval: {
      type: Number,
      default: 60, // seconds
      min: 10,
      max: 3600
    },
    reportingMode: {
      type: String,
      enum: ['continuous', 'interval', 'on_demand', 'geofence_only'],
      default: 'interval'
    },
    powerSaving: {
      enabled: { type: Boolean, default: false },
      mode: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium'
      },
      schedules: [{
        name: String,
        startTime: String, // HH:MM format
        endTime: String,
        days: [Number], // 0-6 (Sunday-Saturday)
        interval: Number // seconds
      }]
    },
    alerts: {
      lowBattery: {
        enabled: { type: Boolean, default: true },
        threshold: { type: Number, default: 20 }
      },
      offline: {
        enabled: { type: Boolean, default: true },
        timeout: { type: Number, default: 300 } // seconds
      },
      speeding: {
        enabled: { type: Boolean, default: false },
        threshold: { type: Number, default: 80 } // km/h
      },
      panic: {
        enabled: { type: Boolean, default: false },
        button: String // GPIO pin or button identifier
      }
    },
    privacy: {
      shareLocation: { type: Boolean, default: true },
      anonymizeData: { type: Boolean, default: false },
      dataRetention: { type: Number, default: 365 } // days
    }
  },
  
  // Security & Access
  security: {
    apiKey: {
      type: String,
      unique: true,
      default: () => `${nanoid(8)}-${nanoid(8)}-${nanoid(8)}-${nanoid(8)}`,
      select: false
    },
    encryptionKey: {
      type: String,
      default: () => crypto.randomBytes(32).toString('hex'),
      select: false
    },
    certificates: [{
      type: String, // 'client', 'server'
      certificate: String,
      privateKey: { type: String, select: false },
      expiresAt: Date,
      isActive: { type: Boolean, default: true }
    }],
    accessControl: {
      allowedUsers: [{
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        },
        permissions: [String],
        grantedAt: { type: Date, default: Date.now },
        grantedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        }
      }],
      ipWhitelist: [String],
      requireEncryption: { type: Boolean, default: false }
    }
  },
  
  // Statistics & Analytics
  statistics: {
    totalDistance: { type: Number, default: 0 }, // meters
    totalTravelTime: { type: Number, default: 0 }, // seconds
    averageSpeed: { type: Number, default: 0 }, // km/h
    maxSpeed: { type: Number, default: 0 }, // km/h
    locationsCount: { type: Number, default: 0 },
    alertsCount: { type: Number, default: 0 },
    lastReset: { type: Date, default: Date.now },
    dailyStats: [{
      date: { type: Date, index: true },
      distance: Number,
      travelTime: Number,
      averageSpeed: Number,
      maxSpeed: Number,
      locationsCount: Number,
      alertsCount: Number
    }]
  },
  
  // Maintenance & Service
  maintenance: {
    schedule: [{
      type: String, // 'distance', 'time', 'usage'
      interval: Number,
      lastService: Date,
      nextService: Date,
      description: String,
      isActive: { type: Boolean, default: true }
    }],
    history: [{
      date: { type: Date, default: Date.now },
      type: String,
      description: String,
      cost: Number,
      serviceProvider: String,
      notes: String
    }],
    warranty: {
      startDate: Date,
      endDate: Date,
      provider: String,
      terms: String
    }
  },
  
  // Integration & External Services
  integrations: {
    obd: {
      enabled: { type: Boolean, default: false },
      protocol: String,
      parameters: [{
        pid: String,
        name: String,
        unit: String,
        enabled: { type: Boolean, default: true }
      }]
    },
    canBus: {
      enabled: { type: Boolean, default: false },
      baudRate: Number,
      filters: [String]
    },
    external: [{
      name: String,
      type: String,
      endpoint: String,
      apiKey: { type: String, select: false },
      enabled: { type: Boolean, default: true },
      lastSync: Date
    }]
  },
  
  // Compliance & Regulations
  compliance: {
    regulations: [String], // e.g., 'GDPR', 'CCPA', 'DOT'
    certifications: [String],
    dataClassification: {
      type: String,
      enum: ['public', 'internal', 'confidential', 'restricted'],
      default: 'internal'
    },
    auditLog: [{
      action: String,
      timestamp: { type: Date, default: Date.now },
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      details: mongoose.Schema.Types.Mixed
    }]
  },
  
  // Metadata
  metadata: {
    tags: [String],
    customFields: [{
      key: String,
      value: mongoose.Schema.Types.Mixed,
      type: String
    }],
    notes: String,
    images: [{
      url: String,
      publicId: String,
      caption: String,
      uploadedAt: { type: Date, default: Date.now }
    }],
    documents: [{
      name: String,
      url: String,
      type: String,
      size: Number,
      uploadedAt: { type: Date, default: Date.now }
    }]
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.security.apiKey;
      delete ret.security.encryptionKey;
      delete ret.security.certificates;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Compound Indexes for Performance
deviceSchema.index({ owner: 1, isActive: 1 });
deviceSchema.index({ tenantId: 1, 'status.isActive': 1 });
deviceSchema.index({ type: 1, category: 1 });
deviceSchema.index({ 'status.isOnline': 1, 'status.lastSeen': -1 });
deviceSchema.index({ 'status.batteryLevel': 1, 'status.isOnline': 1 });
deviceSchema.index({ 'location.current.coordinates': '2dsphere' });
deviceSchema.index({ createdAt: -1 });

// Text Index for Search
deviceSchema.index({
  name: 'text',
  description: 'text',
  deviceId: 'text',
  'hardware.manufacturer': 'text',
  'hardware.model': 'text'
});

// TTL Index for Inactive Devices
deviceSchema.index({ 
  'status.lastSeen': 1 
}, { 
  expireAfterSeconds: 7776000, // 90 days
  partialFilterExpression: { 
    'status.isActive': false,
    'status.isOnline': false
  }
});

// Virtual Properties
deviceSchema.virtual('isOnline').get(function() {
  if (!this.status.lastSeen) return false;
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.status.lastSeen > fiveMinutesAgo;
});

deviceSchema.virtual('batteryStatus').get(function() {
  const level = this.status.batteryLevel;
  if (level === undefined || level === null) return 'unknown';
  if (level <= 10) return 'critical';
  if (level <= 20) return 'low';
  if (level <= 50) return 'medium';
  return 'high';
});

deviceSchema.virtual('currentLocation').get(function() {
  if (!this.location.current.coordinates || this.location.current.coordinates.length !== 2) {
    return null;
  }
  return {
    latitude: this.location.current.coordinates[1],
    longitude: this.location.current.coordinates[0],
    accuracy: this.location.current.accuracy,
    timestamp: this.location.current.timestamp
  };
});

// Pre-save Middleware
deviceSchema.pre('save', function(next) {
  // Update online status based on last seen
  if (this.status.lastSeen) {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    this.status.isOnline = this.status.lastSeen > fiveMinutesAgo;
  }
  
  // Generate API key if not exists
  if (!this.security.apiKey) {
    this.security.apiKey = `${nanoid(8)}-${nanoid(8)}-${nanoid(8)}-${nanoid(8)}`;
  }
  
  next();
});

// Instance Methods
deviceSchema.methods.updateLocation = async function(longitude, latitude, address = null) {
  this.location.current = {
    type: 'Point',
    coordinates: [longitude, latitude],
    timestamp: new Date(),
    address: address ? { formatted: address } : undefined
  };
  
  this.status.lastSeen = new Date();
  this.status.isOnline = true;
  
  return this.save();
};

deviceSchema.methods.updateBattery = async function(level, voltage = null, isCharging = null) {
  this.status.batteryLevel = level;
  if (voltage !== null) this.status.batteryVoltage = voltage;
  if (isCharging !== null) this.status.isCharging = isCharging;
  
  return this.save();
};

deviceSchema.methods.addGeofenceStatus = function(geofenceId, status) {
  const existing = this.location.geofences.find(g => 
    g.geofenceId.toString() === geofenceId.toString()
  );
  
  if (existing) {
    existing.status = status;
    if (status === 'inside') {
      existing.enteredAt = new Date();
      existing.exitedAt = undefined;
    } else if (status === 'outside') {
      existing.exitedAt = new Date();
    }
  } else {
    this.location.geofences.push({
      geofenceId,
      status,
      enteredAt: status === 'inside' ? new Date() : undefined
    });
  }
  
  return this.save();
};

deviceSchema.methods.generateNewApiKey = function() {
  this.security.apiKey = `${nanoid(8)}-${nanoid(8)}-${nanoid(8)}-${nanoid(8)}`;
  return this.save();
};

deviceSchema.methods.addMaintenanceRecord = function(type, description, cost = null) {
  this.maintenance.history.push({
    type,
    description,
    cost,
    date: new Date()
  });
  
  return this.save();
};

deviceSchema.methods.updateDailyStats = function(distance, travelTime, maxSpeed) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let dailyStat = this.statistics.dailyStats.find(stat => 
    stat.date.getTime() === today.getTime()
  );
  
  if (!dailyStat) {
    dailyStat = {
      date: today,
      distance: 0,
      travelTime: 0,
      maxSpeed: 0,
      locationsCount: 0,
      alertsCount: 0
    };
    this.statistics.dailyStats.push(dailyStat);
  }
  
  dailyStat.distance += distance;
  dailyStat.travelTime += travelTime;
  dailyStat.maxSpeed = Math.max(dailyStat.maxSpeed, maxSpeed);
  dailyStat.locationsCount += 1;
  
  // Update overall statistics
  this.statistics.totalDistance += distance;
  this.statistics.totalTravelTime += travelTime;
  this.statistics.maxSpeed = Math.max(this.statistics.maxSpeed, maxSpeed);
  this.statistics.locationsCount += 1;
  
  // Calculate average speed
  if (this.statistics.totalTravelTime > 0) {
    this.statistics.averageSpeed = (this.statistics.totalDistance / 1000) / 
                                   (this.statistics.totalTravelTime / 3600);
  }
  
  return this.save();
};

// Static Methods
deviceSchema.statics.findByDeviceId = function(deviceId) {
  return this.findOne({ 
    deviceId: deviceId.toUpperCase(), 
    'status.isActive': true 
  });
};

deviceSchema.statics.findByOwner = function(ownerId, options = {}) {
  const query = { owner: ownerId };
  
  if (options.active !== undefined) {
    query['status.isActive'] = options.active;
  }
  
  if (options.online !== undefined) {
    query['status.isOnline'] = options.online;
  }
  
  if (options.type) {
    query.type = options.type;
  }
  
  return this.find(query).sort({ name: 1 });
};

deviceSchema.statics.findNearby = function(longitude, latitude, maxDistance = 1000) {
  return this.find({
    'location.current.coordinates': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    },
    'status.isActive': true,
    'status.isOnline': true
  });
};

deviceSchema.statics.getStatsByOwner = async function(ownerId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        owner: mongoose.Types.ObjectId(ownerId),
        'status.isActive': true
      }
    },
    {
      $group: {
        _id: null,
        totalDevices: { $sum: 1 },
        onlineDevices: {
          $sum: { $cond: ['$status.isOnline', 1, 0] }
        },
        totalDistance: { $sum: '$statistics.totalDistance' },
        averageBattery: { $avg: '$status.batteryLevel' },
        lowBatteryDevices: {
          $sum: { $cond: [{ $lte: ['$status.batteryLevel', 20] }, 1, 0] }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Device', deviceSchema);
