const Location = require('../models/Location');
const Device = require('../models/Device');
const Geofence = require('../models/Geofence');
const Alert = require('../models/Alert');
const logger = require('../utils/logger');

/**
 * Location Controller for GPS Tracking
 */
class LocationController {
  /**
   * Update device location
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateLocation(req, res) {
    try {
      const {
        latitude,
        longitude,
        accuracy,
        altitude,
        altitudeAccuracy,
        speed,
        heading,
        batteryLevel,
        signalStrength,
        timestamp,
        address
      } = req.body;
      
      const device = req.device; // Set by API key validation middleware
      
      // Create location record
      const locationData = {
        device: device._id,
        location: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        accuracy,
        altitude,
        altitudeAccuracy,
        speed,
        heading,
        batteryLevel,
        signalStrength,
        timestamp: timestamp ? new Date(timestamp) : new Date(),
        address: address ? { formatted: address } : undefined
      };
      
      const location = new Location(locationData);
      await location.save();
      
      // Update device's last known location and status
      await device.updateLocation(longitude, latitude, address);
      
      if (batteryLevel !== undefined) {
        await device.updateBattery(batteryLevel);
      }
      
      // Check geofences and trigger alerts
      await LocationController.checkGeofences(device, latitude, longitude);
      
      // Emit real-time update (if Socket.IO is available)
      if (req.app.get('io')) {
        req.app.get('io').to(`device_${device._id}`).emit('locationUpdate', {
          deviceId: device.deviceId,
          location: {
            latitude,
            longitude,
            timestamp: location.timestamp
          },
          batteryLevel,
          speed
        });
      }
      
      logger.info(`Location updated for device: ${device.deviceId}`);
      
      res.json({
        success: true,
        message: 'Location updated successfully',
        data: {
          locationId: location._id,
          timestamp: location.timestamp
        }
      });
      
    } catch (error) {
      logger.error('Location update error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update location'
      });
    }
  }
  
  /**
   * Get location history for a device
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getLocationHistory(req, res) {
    try {
      const { id: deviceId } = req.params;
      const { startDate, endDate, limit = 100, page = 1 } = req.query;
      const userId = req.user._id;
      
      // Verify device ownership
      const device = await Device.findOne({
        _id: deviceId,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      // Build query
      const query = { device: deviceId };
      
      if (startDate || endDate) {
        query.timestamp = {};
        if (startDate) query.timestamp.$gte = new Date(startDate);
        if (endDate) query.timestamp.$lte = new Date(endDate);
      }
      
      // Execute query with pagination
      const locations = await Location.find(query)
        .sort({ timestamp: -1 })
        .limit(parseInt(limit))
        .skip((parseInt(page) - 1) * parseInt(limit))
        .select('location timestamp speed heading batteryLevel accuracy address movement');
      
      const total = await Location.countDocuments(query);
      
      // Transform data for response
      const transformedLocations = locations.map(loc => ({
        id: loc._id,
        latitude: loc.location.coordinates[1],
        longitude: loc.location.coordinates[0],
        timestamp: loc.timestamp,
        speed: loc.speed,
        heading: loc.heading,
        batteryLevel: loc.batteryLevel,
        accuracy: loc.accuracy,
        address: loc.address?.formatted,
        movement: loc.movement
      }));
      
      res.json({
        success: true,
        data: {
          locations: transformedLocations,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
      
    } catch (error) {
      logger.error('Get location history error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get location history'
      });
    }
  }
  
  /**
   * Get current location for a device
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getCurrentLocation(req, res) {
    try {
      const { id: deviceId } = req.params;
      const userId = req.user._id;
      
      // Verify device ownership
      const device = await Device.findOne({
        _id: deviceId,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      // Get latest location
      const location = await Location.getLatest(deviceId);
      
      if (!location) {
        return res.status(404).json({
          success: false,
          message: 'No location data found for this device'
        });
      }
      
      const response = {
        id: location._id,
        deviceId: device.deviceId,
        deviceName: device.name,
        latitude: location.location.coordinates[1],
        longitude: location.location.coordinates[0],
        timestamp: location.timestamp,
        speed: location.speed,
        heading: location.heading,
        batteryLevel: location.batteryLevel,
        accuracy: location.accuracy,
        address: location.address?.formatted,
        isOnline: device.isOnline,
        lastSeen: device.lastSeen
      };
      
      res.json({
        success: true,
        data: { location: response }
      });
      
    } catch (error) {
      logger.error('Get current location error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get current location'
      });
    }
  }
  
  /**
   * Get locations within a geographic area
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getLocationsInArea(req, res) {
    try {
      const { id: deviceId } = req.params;
      const { latitude, longitude, radius = 1000 } = req.query;
      const userId = req.user._id;
      
      // Verify device ownership
      const device = await Device.findOne({
        _id: deviceId,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      if (!latitude || !longitude) {
        return res.status(400).json({
          success: false,
          message: 'Latitude and longitude are required'
        });
      }
      
      // Find locations within the specified radius
      const locations = await Location.findInArea(
        deviceId,
        [parseFloat(longitude), parseFloat(latitude)],
        parseInt(radius)
      );
      
      const transformedLocations = locations.map(loc => ({
        id: loc._id,
        latitude: loc.location.coordinates[1],
        longitude: loc.location.coordinates[0],
        timestamp: loc.timestamp,
        speed: loc.speed,
        address: loc.address?.formatted
      }));
      
      res.json({
        success: true,
        data: {
          locations: transformedLocations,
          center: {
            latitude: parseFloat(latitude),
            longitude: parseFloat(longitude)
          },
          radius: parseInt(radius),
          count: transformedLocations.length
        }
      });
      
    } catch (error) {
      logger.error('Get locations in area error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get locations in area'
      });
    }
  }
  
  /**
   * Check geofences for location and trigger alerts
   * @param {Object} device - Device object
   * @param {number} latitude - Latitude
   * @param {number} longitude - Longitude
   */
  static async checkGeofences(device, latitude, longitude) {
    try {
      // Find geofences that contain this location
      const containingGeofences = await Geofence.findContaining(
        longitude,
        latitude,
        { owner: device.owner, devices: [device._id] }
      );
      
      // Get previous location to determine entry/exit
      const previousLocation = await Location.findOne({
        device: device._id
      }).sort({ timestamp: -1 }).skip(1);
      
      for (const geofence of containingGeofences) {
        let eventType = 'inside';
        
        // Check if this is an entry event
        if (previousLocation) {
          const wasInside = geofence.containsPoint(
            previousLocation.location.coordinates[0],
            previousLocation.location.coordinates[1]
          );
          
          if (!wasInside) {
            eventType = 'enter';
          }
        } else {
          eventType = 'enter'; // First location update
        }
        
        // Create alert for geofence entry
        if (eventType === 'enter' && geofence.alerts.onEnter.enabled) {
          await LocationController.createGeofenceAlert(
            device,
            geofence,
            'geofence_enter',
            latitude,
            longitude
          );
        }
      }
      
      // Check for geofence exits
      if (previousLocation) {
        const previousGeofences = await Geofence.findContaining(
          previousLocation.location.coordinates[0],
          previousLocation.location.coordinates[1],
          { owner: device.owner, devices: [device._id] }
        );
        
        for (const prevGeofence of previousGeofences) {
          const stillInside = prevGeofence.containsPoint(longitude, latitude);
          
          if (!stillInside && prevGeofence.alerts.onExit.enabled) {
            await LocationController.createGeofenceAlert(
              device,
              prevGeofence,
              'geofence_exit',
              latitude,
              longitude
            );
          }
        }
      }
      
    } catch (error) {
      logger.error('Geofence check error:', error);
    }
  }
  
  /**
   * Create geofence alert
   * @param {Object} device - Device object
   * @param {Object} geofence - Geofence object
   * @param {string} type - Alert type
   * @param {number} latitude - Latitude
   * @param {number} longitude - Longitude
   */
  static async createGeofenceAlert(device, geofence, type, latitude, longitude) {
    try {
      const alertData = {
        type,
        severity: geofence.priority === 'critical' ? 'critical' : 'warning',
        device: device._id,
        user: device.owner,
        geofence: geofence._id,
        title: `Geofence ${type.split('_')[1]} - ${geofence.name}`,
        message: type === 'geofence_enter' 
          ? geofence.alerts.onEnter.message 
          : geofence.alerts.onExit.message,
        data: {
          coordinates: { latitude, longitude },
          geofenceName: geofence.name,
          deviceName: device.name
        },
        triggeredAt: new Date()
      };
      
      const alert = new Alert(alertData);
      await alert.save();
      
      logger.info(`Geofence alert created: ${type} for device ${device.deviceId}`);
      
    } catch (error) {
      logger.error('Create geofence alert error:', error);
    }
  }
}

module.exports = LocationController;
