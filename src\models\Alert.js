const mongoose = require('mongoose');

/**
 * <PERSON>ert <PERSON> for GPS Tracking System
 */
const alertSchema = new mongoose.Schema({
  // Alert Identification
  alertId: {
    type: String,
    unique: true,
    default: function() {
      return 'ALT-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
  },
  
  // Alert Type and Category
  type: {
    type: String,
    enum: [
      'geofence_enter',
      'geofence_exit',
      'geofence_dwell',
      'speed_limit',
      'low_battery',
      'device_offline',
      'panic_button',
      'route_deviation',
      'anomaly_detected',
      'maintenance_due',
      'custom'
    ],
    required: [true, 'Alert type is required']
  },
  
  severity: {
    type: String,
    enum: ['info', 'warning', 'critical', 'emergency'],
    default: 'info'
  },
  
  // Related Entities
  device: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device',
    required: [true, 'Device reference is required'],
    index: true
  },
  
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User reference is required'],
    index: true
  },
  
  geofence: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Geofence',
    default: null
  },
  
  location: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Location',
    default: null
  },
  
  // Alert Content
  title: {
    type: String,
    required: [true, 'Alert title is required'],
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  
  message: {
    type: String,
    required: [true, 'Alert message is required'],
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  
  // Alert Data
  data: {
    // Location information
    coordinates: {
      longitude: Number,
      latitude: Number
    },
    
    address: String,
    
    // Speed information
    speed: Number,
    speedLimit: Number,
    
    // Battery information
    batteryLevel: Number,
    batteryThreshold: Number,
    
    // Geofence information
    geofenceName: String,
    dwellTime: Number,
    
    // Device information
    deviceName: String,
    lastSeen: Date,
    
    // Custom data
    customData: mongoose.Schema.Types.Mixed
  },
  
  // Alert Status
  status: {
    type: String,
    enum: ['active', 'acknowledged', 'resolved', 'dismissed'],
    default: 'active',
    index: true
  },
  
  // Timestamps
  triggeredAt: {
    type: Date,
    required: [true, 'Triggered timestamp is required'],
    index: true
  },
  
  acknowledgedAt: {
    type: Date,
    default: null
  },
  
  resolvedAt: {
    type: Date,
    default: null
  },
  
  // Acknowledgment Information
  acknowledgedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  acknowledgmentNote: {
    type: String,
    maxlength: [500, 'Acknowledgment note cannot exceed 500 characters']
  },
  
  // Notification Status
  notifications: {
    email: {
      sent: { type: Boolean, default: false },
      sentAt: { type: Date, default: null },
      recipients: [String],
      error: String
    },
    
    sms: {
      sent: { type: Boolean, default: false },
      sentAt: { type: Date, default: null },
      recipients: [String],
      error: String
    },
    
    push: {
      sent: { type: Boolean, default: false },
      sentAt: { type: Date, default: null },
      recipients: [String],
      error: String
    },
    
    webhook: {
      sent: { type: Boolean, default: false },
      sentAt: { type: Date, default: null },
      url: String,
      error: String
    }
  },
  
  // Alert Rules and Conditions
  rule: {
    id: String,
    name: String,
    conditions: mongoose.Schema.Types.Mixed
  },
  
  // Priority and Escalation
  priority: {
    type: Number,
    min: [1, 'Priority must be at least 1'],
    max: [10, 'Priority cannot exceed 10'],
    default: 5
  },
  
  escalated: {
    type: Boolean,
    default: false
  },
  
  escalationLevel: {
    type: Number,
    default: 0
  },
  
  // Metadata
  tags: [String],
  
  metadata: {
    source: String,
    version: String,
    correlationId: String,
    additionalInfo: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
alertSchema.index({ device: 1, triggeredAt: -1 });
alertSchema.index({ user: 1, status: 1, triggeredAt: -1 });
alertSchema.index({ type: 1, severity: 1 });
alertSchema.index({ status: 1, triggeredAt: -1 });
alertSchema.index({ geofence: 1 });
alertSchema.index({ escalated: 1, priority: -1 });
alertSchema.index({ tags: 1 });

// TTL index for automatic cleanup (remove resolved alerts after 90 days)
alertSchema.index({ resolvedAt: 1 }, { 
  expireAfterSeconds: 7776000, // 90 days
  partialFilterExpression: { status: 'resolved' }
});

// Virtual for alert age
alertSchema.virtual('age').get(function() {
  return Date.now() - this.triggeredAt.getTime();
});

// Virtual for response time
alertSchema.virtual('responseTime').get(function() {
  if (!this.acknowledgedAt) return null;
  return this.acknowledgedAt.getTime() - this.triggeredAt.getTime();
});

// Virtual for resolution time
alertSchema.virtual('resolutionTime').get(function() {
  if (!this.resolvedAt) return null;
  return this.resolvedAt.getTime() - this.triggeredAt.getTime();
});

// Virtual for notification summary
alertSchema.virtual('notificationSummary').get(function() {
  const summary = {
    total: 0,
    sent: 0,
    failed: 0
  };
  
  Object.values(this.notifications).forEach(notification => {
    if (typeof notification === 'object' && notification.sent !== undefined) {
      summary.total++;
      if (notification.sent) {
        summary.sent++;
      } else if (notification.error) {
        summary.failed++;
      }
    }
  });
  
  return summary;
});

// Instance method to acknowledge alert
alertSchema.methods.acknowledge = function(userId, note = '') {
  this.status = 'acknowledged';
  this.acknowledgedAt = new Date();
  this.acknowledgedBy = userId;
  this.acknowledgmentNote = note;
  
  return this.save();
};

// Instance method to resolve alert
alertSchema.methods.resolve = function(userId, note = '') {
  this.status = 'resolved';
  this.resolvedAt = new Date();
  
  if (!this.acknowledgedAt) {
    this.acknowledgedAt = new Date();
    this.acknowledgedBy = userId;
  }
  
  if (note) {
    this.acknowledgmentNote = note;
  }
  
  return this.save();
};

// Instance method to dismiss alert
alertSchema.methods.dismiss = function(userId, note = '') {
  this.status = 'dismissed';
  this.acknowledgedAt = new Date();
  this.acknowledgedBy = userId;
  this.acknowledgmentNote = note;
  
  return this.save();
};

// Instance method to escalate alert
alertSchema.methods.escalate = function() {
  this.escalated = true;
  this.escalationLevel += 1;
  this.priority = Math.min(this.priority + 2, 10);
  
  return this.save();
};

// Static method to find active alerts
alertSchema.statics.findActive = function(options = {}) {
  const query = { status: 'active' };
  
  if (options.user) {
    query.user = options.user;
  }
  
  if (options.device) {
    query.device = options.device;
  }
  
  if (options.type) {
    query.type = options.type;
  }
  
  if (options.severity) {
    query.severity = options.severity;
  }
  
  return this.find(query)
    .populate('device', 'name deviceId type')
    .populate('user', 'username email firstName lastName')
    .populate('geofence', 'name type')
    .sort({ priority: -1, triggeredAt: -1 });
};

// Static method to find alerts by time range
alertSchema.statics.findByTimeRange = function(startTime, endTime, options = {}) {
  const query = {
    triggeredAt: {
      $gte: startTime,
      $lte: endTime
    }
  };
  
  if (options.user) {
    query.user = options.user;
  }
  
  if (options.device) {
    query.device = options.device;
  }
  
  return this.find(query).sort({ triggeredAt: -1 });
};

// Static method to get alert statistics
alertSchema.statics.getStatistics = function(userId, timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        user: mongoose.Types.ObjectId(userId),
        triggeredAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        active: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
        acknowledged: { $sum: { $cond: [{ $eq: ['$status', 'acknowledged'] }, 1, 0] } },
        resolved: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } },
        critical: { $sum: { $cond: [{ $eq: ['$severity', 'critical'] }, 1, 0] } },
        emergency: { $sum: { $cond: [{ $eq: ['$severity', 'emergency'] }, 1, 0] } },
        avgResponseTime: { $avg: '$responseTime' },
        avgResolutionTime: { $avg: '$resolutionTime' }
      }
    }
  ]);
};

module.exports = mongoose.model('Alert', alertSchema);
