const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const passport = require('passport');
const config = require('config');
const logger = require('../../../shared/utils/logger');
const database = require('../../../shared/utils/database');
const { authenticate, authorize } = require('../../../shared/middleware/auth');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const tenantRoutes = require('./routes/tenants');
const oauthRoutes = require('./routes/oauth');

/**
 * Enterprise Authentication Service
 */
class AuthService {
  constructor() {
    this.app = express();
    this.port = config.get('services.auth.port');
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"]
        }
      },
      crossOriginEmbedderPolicy: false
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key', 'X-Tenant-ID'],
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: {
        success: false,
        message: 'Too many requests from this IP, please try again later'
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // Stricter rate limiting for auth endpoints
    const authLimiter = rateLimit({
      windowMs: 15 * 60 * 1000,
      max: 10,
      message: {
        success: false,
        message: 'Too many authentication attempts, please try again later'
      }
    });
    this.app.use('/api/auth/login', authLimiter);
    this.app.use('/api/auth/register', authLimiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Compression
    this.app.use(compression());

    // Passport initialization
    this.app.use(passport.initialize());

    // Request logging
    this.app.use((req, res, next) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        logger.http(req, res, duration);
      });
      
      next();
    });

    // Request ID and correlation tracking
    this.app.use((req, res, next) => {
      req.requestId = require('uuid').v4();
      req.correlationId = req.get('X-Correlation-ID') || req.requestId;
      res.set('X-Request-ID', req.requestId);
      res.set('X-Correlation-ID', req.correlationId);
      next();
    });
  }

  /**
   * Setup routes
   */
  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        service: 'auth-service',
        version: config.get('app.version'),
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: config.get('app.environment')
      });
    });

    // Service info
    this.app.get('/info', (req, res) => {
      res.json({
        service: 'Enterprise Authentication Service',
        version: config.get('app.version'),
        description: 'Handles user authentication, authorization, and tenant management',
        endpoints: {
          auth: '/api/auth',
          users: '/api/users',
          tenants: '/api/tenants',
          oauth: '/api/oauth'
        },
        features: [
          'JWT Authentication',
          'OAuth2 Integration',
          'Multi-tenant Support',
          'Role-Based Access Control',
          'Two-Factor Authentication',
          'Session Management',
          'API Key Management'
        ]
      });
    });

    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/users', userRoutes);
    this.app.use('/api/tenants', tenantRoutes);
    this.app.use('/api/oauth', oauthRoutes);

    // Metrics endpoint
    this.app.get('/metrics', (req, res) => {
      // Prometheus metrics would be implemented here
      res.set('Content-Type', 'text/plain');
      res.send('# Metrics endpoint - implement Prometheus metrics here');
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'Endpoint not found',
        service: 'auth-service',
        path: req.originalUrl
      });
    });
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // Global error handler
    this.app.use((error, req, res, next) => {
      logger.error('Auth service error', {
        error: error.message,
        stack: error.stack,
        requestId: req.requestId,
        correlationId: req.correlationId,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Don't leak error details in production
      const isDevelopment = config.get('app.environment') === 'development';
      
      res.status(error.status || 500).json({
        success: false,
        message: error.message || 'Internal server error',
        code: error.code || 'INTERNAL_ERROR',
        requestId: req.requestId,
        ...(isDevelopment && { stack: error.stack })
      });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', { promise, reason });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', { error: error.message, stack: error.stack });
      process.exit(1);
    });

    // Graceful shutdown
    const gracefulShutdown = (signal) => {
      logger.info(`Received ${signal}. Graceful shutdown initiated.`);
      
      this.server.close(() => {
        logger.info('HTTP server closed.');
        
        // Close database connections
        database.shutdown().then(() => {
          logger.info('Database connections closed.');
          process.exit(0);
        }).catch((error) => {
          logger.error('Error during database shutdown:', error);
          process.exit(1);
        });
      });

      // Force close after 30 seconds
      setTimeout(() => {
        logger.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }

  /**
   * Start the server
   */
  async start() {
    try {
      // Initialize database connections
      await database.initialize();
      
      // Start HTTP server
      this.server = this.app.listen(this.port, () => {
        logger.info(`🔐 Auth Service running on port ${this.port}`, {
          service: 'auth-service',
          port: this.port,
          environment: config.get('app.environment'),
          version: config.get('app.version')
        });
      });

      // Handle server errors
      this.server.on('error', (error) => {
        if (error.syscall !== 'listen') {
          throw error;
        }

        const bind = typeof this.port === 'string' ? 'Pipe ' + this.port : 'Port ' + this.port;

        switch (error.code) {
          case 'EACCES':
            logger.error(`${bind} requires elevated privileges`);
            process.exit(1);
            break;
          case 'EADDRINUSE':
            logger.error(`${bind} is already in use`);
            process.exit(1);
            break;
          default:
            throw error;
        }
      });

    } catch (error) {
      logger.error('Failed to start auth service:', { error: error.message });
      process.exit(1);
    }
  }
}

// Create and start the service
const authService = new AuthService();

// Start only if not in test environment
if (process.env.NODE_ENV !== 'test') {
  authService.start();
}

module.exports = authService;
