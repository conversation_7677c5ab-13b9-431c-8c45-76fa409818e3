# AI GPS Tracker

An advanced AI-powered GPS tracking system with real-time monitoring, geofencing, intelligent analytics, and anomaly detection.

## Features

### 🚀 Core Features
- **Real-time GPS Tracking**: Live location updates with WebSocket support
- **Device Management**: Register and manage multiple GPS tracking devices
- **User Authentication**: Secure JWT-based authentication system
- **Interactive Dashboard**: Modern web interface with real-time updates

### 🧠 AI Features
- **Route Prediction**: Machine learning-based route prediction algorithms
- **Anomaly Detection**: Intelligent detection of unusual movement patterns
- **Movement Analysis**: Pattern recognition for frequent locations and routes
- **Smart Recommendations**: AI-generated insights for optimization

### 🗺️ Mapping & Geofencing
- **Live Map View**: Interactive map with device locations
- **Geofencing**: Create virtual boundaries with entry/exit alerts
- **Location History**: Track and visualize historical movement data
- **Address Resolution**: Convert coordinates to human-readable addresses

### 📊 Analytics & Reporting
- **Performance Metrics**: Speed, distance, and efficiency analysis
- **Activity Patterns**: Time-based movement pattern analysis
- **Battery Monitoring**: Device battery level tracking and alerts
- **Custom Reports**: Generate detailed analytics reports

### 🔔 Alert System
- **Real-time Alerts**: Instant notifications for important events
- **Multiple Alert Types**: Geofence, speed, battery, and anomaly alerts
- **Notification Channels**: Email, SMS, and push notifications
- **Alert Management**: Acknowledge, resolve, and track alert history

## Technology Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web application framework
- **MongoDB** - NoSQL database with Mongoose ODM
- **Socket.IO** - Real-time bidirectional communication
- **JWT** - JSON Web Token authentication
- **bcryptjs** - Password hashing
- **Winston** - Logging framework

### AI & Machine Learning
- **ml-matrix** - Matrix operations for ML algorithms
- **simple-statistics** - Statistical analysis functions
- **Custom algorithms** - Route prediction and anomaly detection

### Frontend
- **Vanilla JavaScript** - No framework dependencies
- **Leaflet.js** - Interactive maps
- **Chart.js** - Data visualization
- **Socket.IO Client** - Real-time updates
- **Responsive CSS** - Mobile-friendly design

### Security & Performance
- **Helmet.js** - Security headers
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - API protection
- **Input Validation** - express-validator
- **Compression** - Response compression

## Installation

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (v4.4 or higher)
- npm or yarn package manager

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-gps-tracker
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   PORT=3000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/ai-gps-tracker
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRE=7d
   ```

4. **Start MongoDB**
   ```bash
   # Using MongoDB service
   sudo systemctl start mongod
   
   # Or using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

5. **Start the application**
   ```bash
   # Development mode with auto-reload
   npm run dev
   
   # Production mode
   npm start
   ```

6. **Access the application**
   - Dashboard: http://localhost:3000
   - API Documentation: http://localhost:3000/api-docs
   - Health Check: http://localhost:3000/health

## API Documentation

The API is fully documented using Swagger/OpenAPI 3.0. Access the interactive documentation at:
```
http://localhost:3000/api-docs
```

### Key API Endpoints

#### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/logout` - User logout

#### Device Management
- `GET /api/devices` - List user devices
- `POST /api/devices` - Register new device
- `GET /api/devices/:id` - Get device details
- `PUT /api/devices/:id` - Update device
- `DELETE /api/devices/:id` - Delete device

#### Location Tracking
- `POST /api/locations/update` - Update device location (for devices)
- `GET /api/locations/devices/:id/history` - Get location history
- `GET /api/locations/devices/:id/current` - Get current location

#### AI Features
- `GET /api/ai/devices/:id/predict-route` - Get route prediction
- `POST /api/ai/devices/:id/analyze-anomaly` - Analyze location anomaly
- `GET /api/ai/devices/:id/insights` - Get AI insights
- `GET /api/ai/devices/:id/movement-patterns` - Get movement patterns

## Usage

### 1. User Registration
1. Open the dashboard in your browser
2. Click "Register here" on the login modal
3. Fill in your details and submit
4. You'll be automatically logged in

### 2. Device Registration
1. Navigate to the "Devices" section
2. Click "Add Device"
3. Enter device details (Device ID, name, type)
4. The device is now ready to send location updates

### 3. Location Updates
Devices can send location updates using the API:
```javascript
// Example location update
const locationData = {
  latitude: 40.7128,
  longitude: -74.0060,
  accuracy: 10,
  speed: 25,
  heading: 180,
  batteryLevel: 85,
  timestamp: new Date().toISOString()
};

fetch('/api/locations/update', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'YOUR_DEVICE_ID'
  },
  body: JSON.stringify(locationData)
});
```

### 4. Real-time Monitoring
- View live device locations on the map
- Monitor device status in real-time
- Receive instant alerts for important events

### 5. AI Insights
- Select a device in the AI Insights section
- View route predictions and movement patterns
- Get smart recommendations for optimization

## Development

### Project Structure
```
ai-gps-tracker/
├── src/
│   ├── config/          # Configuration files
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Express middleware
│   ├── models/          # MongoDB models
│   ├── routes/          # API routes
│   ├── services/        # Business logic services
│   ├── utils/           # Utility functions
│   └── server.js        # Main server file
├── public/              # Frontend assets
│   ├── css/            # Stylesheets
│   ├── js/             # JavaScript files
│   └── index.html      # Main HTML file
├── tests/              # Test files
├── package.json        # Dependencies
└── README.md          # This file
```

### Available Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## Security Considerations

- All API endpoints are protected with authentication
- Passwords are hashed using bcrypt
- JWT tokens have expiration times
- Input validation on all endpoints
- Rate limiting to prevent abuse
- CORS configuration for cross-origin requests
- Security headers with Helmet.js

## Performance Optimization

- Database indexing for fast queries
- Response compression
- Efficient real-time updates with Socket.IO
- Pagination for large datasets
- Caching strategies for frequently accessed data

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the API documentation
- Review the code examples in this README

## Roadmap

- [ ] Mobile app development
- [ ] Advanced geofencing features
- [ ] Integration with external mapping services
- [ ] Enhanced AI algorithms
- [ ] Multi-tenant support
- [ ] Advanced reporting features
- [ ] Third-party integrations
