# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/ai-gps-tracker
MONGODB_TEST_URI=mongodb://localhost:27017/ai-gps-tracker-test

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_REFRESH_EXPIRE=30d

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# AI/ML Configuration
PREDICTION_MODEL_PATH=./models/route_prediction.json
ANOMALY_THRESHOLD=0.8
LEARNING_RATE=0.01

# Geofencing Configuration
DEFAULT_GEOFENCE_RADIUS=100
MAX_GEOFENCES_PER_USER=50

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# External APIs (optional)
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
WEATHER_API_KEY=your-weather-api-key

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret

# Real-time Configuration
SOCKET_IO_CORS_ORIGIN=http://localhost:3000
MAX_LOCATION_HISTORY=1000
LOCATION_UPDATE_INTERVAL=5000
