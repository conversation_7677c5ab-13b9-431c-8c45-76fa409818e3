const mongoose = require('mongoose');

/**
 * Enterprise Location Schema optimized for Time-Series Data and High-Volume GPS Tracking
 */
const locationSchema = new mongoose.Schema({
  // Sharding Key - Critical for horizontal scaling
  device: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device',
    required: [true, 'Device reference is required'],
    index: true
  },
  
  // Time-series partition key
  timestamp: {
    type: Date,
    required: [true, 'Timestamp is required'],
    default: Date.now,
    index: true
  },
  
  // Geospatial Data
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true,
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: [true, 'Coordinates are required'],
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates format or range'
      },
      index: '2dsphere'
    }
  },
  
  // GPS Quality Metrics
  accuracy: {
    type: Number,
    min: [0, 'Accuracy cannot be negative'],
    max: [10000, 'Accuracy seems unrealistic'],
    unit: 'meters'
  },
  
  altitude: {
    type: Number,
    min: [-500, 'Altitude below sea level limit'],
    max: [50000, 'Altitude above realistic limit'],
    unit: 'meters'
  },
  
  altitudeAccuracy: {
    type: Number,
    min: [0, 'Altitude accuracy cannot be negative'],
    unit: 'meters'
  },
  
  // Movement Data
  speed: {
    type: Number,
    min: [0, 'Speed cannot be negative'],
    max: [1000, 'Speed above realistic limit'],
    default: 0,
    unit: 'km/h',
    index: true
  },
  
  heading: {
    type: Number,
    min: [0, 'Heading must be between 0 and 360'],
    max: [360, 'Heading must be between 0 and 360'],
    unit: 'degrees'
  },
  
  // Device Status at Time of Reading
  deviceStatus: {
    batteryLevel: {
      type: Number,
      min: 0,
      max: 100,
      unit: 'percentage'
    },
    batteryVoltage: {
      type: Number,
      min: 0,
      max: 50,
      unit: 'volts'
    },
    isCharging: Boolean,
    signalStrength: {
      type: Number,
      min: -120,
      max: 0,
      unit: 'dBm'
    },
    temperature: {
      type: Number,
      min: -50,
      max: 100,
      unit: 'celsius'
    },
    satellites: {
      type: Number,
      min: 0,
      max: 50
    }
  },
  
  // Movement Analysis (calculated fields)
  movement: {
    distance: {
      type: Number,
      default: 0,
      min: 0,
      unit: 'meters'
    },
    duration: {
      type: Number,
      default: 0,
      min: 0,
      unit: 'seconds'
    },
    averageSpeed: {
      type: Number,
      default: 0,
      min: 0,
      unit: 'km/h'
    },
    bearing: {
      type: Number,
      min: 0,
      max: 360,
      unit: 'degrees'
    },
    isStationary: {
      type: Boolean,
      default: false
    },
    stationaryDuration: {
      type: Number,
      default: 0,
      unit: 'seconds'
    }
  },
  
  // Address Information (reverse geocoding)
  address: {
    formatted: String,
    components: {
      streetNumber: String,
      street: String,
      neighborhood: String,
      city: String,
      county: String,
      state: String,
      country: String,
      postalCode: String,
      countryCode: String
    },
    placeId: String,
    confidence: {
      type: Number,
      min: 0,
      max: 1
    },
    source: {
      type: String,
      enum: ['google', 'mapbox', 'nominatim', 'here', 'cached'],
      default: 'google'
    }
  },
  
  // Geofence Information
  geofences: [{
    geofenceId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Geofence'
    },
    name: String,
    status: {
      type: String,
      enum: ['inside', 'outside', 'entering', 'exiting'],
      required: true
    },
    distance: {
      type: Number,
      unit: 'meters'
    },
    dwellTime: {
      type: Number,
      default: 0,
      unit: 'seconds'
    }
  }],
  
  // Data Quality & Validation
  quality: {
    score: {
      type: Number,
      min: 0,
      max: 100,
      default: 100
    },
    flags: [{
      type: String,
      enum: [
        'low_accuracy', 'speed_anomaly', 'location_jump', 
        'duplicate', 'out_of_sequence', 'invalid_coordinates',
        'poor_signal', 'indoor_location', 'tunnel_exit'
      ]
    }],
    source: {
      type: String,
      enum: ['gps', 'network', 'passive', 'fused', 'manual'],
      default: 'gps'
    },
    provider: String, // GPS provider info
    hdop: Number, // Horizontal Dilution of Precision
    vdop: Number, // Vertical Dilution of Precision
    pdop: Number  // Position Dilution of Precision
  },
  
  // AI/ML Analysis Results
  analysis: {
    anomalyScore: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    anomalyReasons: [String],
    behaviorPattern: {
      type: String,
      enum: ['normal', 'commuting', 'leisure', 'work', 'emergency', 'unknown'],
      default: 'unknown'
    },
    routeSegment: {
      segmentId: String,
      confidence: Number,
      type: String // 'highway', 'city', 'residential', etc.
    },
    predictions: {
      nextLocation: {
        coordinates: [Number],
        confidence: Number,
        timeToReach: Number // seconds
      },
      destination: {
        coordinates: [Number],
        confidence: Number,
        eta: Date
      }
    }
  },
  
  // Raw Sensor Data (optional, for advanced analysis)
  rawData: {
    accelerometer: {
      x: Number,
      y: Number,
      z: Number
    },
    gyroscope: {
      x: Number,
      y: Number,
      z: Number
    },
    magnetometer: {
      x: Number,
      y: Number,
      z: Number
    },
    obd: [{
      pid: String,
      value: Number,
      unit: String
    }],
    canBus: [{
      id: String,
      data: String,
      timestamp: Date
    }]
  },
  
  // Data Processing Metadata
  processing: {
    receivedAt: {
      type: Date,
      default: Date.now
    },
    processedAt: Date,
    version: {
      type: String,
      default: '1.0'
    },
    pipeline: [String], // Processing steps applied
    latency: {
      type: Number,
      unit: 'milliseconds'
    },
    batchId: String,
    correlationId: String
  },
  
  // Tenant Information (for multi-tenancy)
  tenantId: {
    type: String,
    index: true
  },
  
  // Data Retention
  retention: {
    expiresAt: {
      type: Date,
      index: { expireAfterSeconds: 0 }
    },
    archived: {
      type: Boolean,
      default: false
    },
    archiveLocation: String
  }
}, {
  timestamps: false, // We use custom timestamp field
  collection: 'locations', // Explicit collection name
  // Optimize for time-series queries
  timeseries: {
    timeField: 'timestamp',
    metaField: 'device',
    granularity: 'minutes'
  }
});

// Compound Indexes for High-Performance Queries
locationSchema.index({ device: 1, timestamp: -1 }); // Primary query pattern
locationSchema.index({ device: 1, timestamp: 1, 'location.coordinates': '2dsphere' }); // Geospatial + time
locationSchema.index({ timestamp: -1, device: 1 }); // Time-based queries
locationSchema.index({ 'location.coordinates': '2dsphere', timestamp: -1 }); // Geospatial queries
locationSchema.index({ device: 1, speed: -1, timestamp: -1 }); // Speed analysis
locationSchema.index({ tenantId: 1, timestamp: -1 }); // Multi-tenant queries

// Sparse indexes for optional fields
locationSchema.index({ 'geofences.geofenceId': 1 }, { sparse: true });
locationSchema.index({ 'analysis.anomalyScore': -1 }, { sparse: true });
locationSchema.index({ 'quality.score': 1 }, { sparse: true });

// TTL Index for automatic data cleanup
locationSchema.index({ 'retention.expiresAt': 1 }, { expireAfterSeconds: 0 });

// Partitioning hint for sharding (device + timestamp)
locationSchema.index({ device: 'hashed' }); // For even distribution

// Virtual Properties
locationSchema.virtual('latitude').get(function() {
  return this.location.coordinates[1];
});

locationSchema.virtual('longitude').get(function() {
  return this.location.coordinates[0];
});

locationSchema.virtual('age').get(function() {
  return Date.now() - this.timestamp.getTime();
});

locationSchema.virtual('isRecent').get(function() {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.timestamp > fiveMinutesAgo;
});

// Pre-save Middleware
locationSchema.pre('save', function(next) {
  // Set processing timestamp
  if (!this.processing.processedAt) {
    this.processing.processedAt = new Date();
  }
  
  // Calculate processing latency
  if (this.processing.receivedAt) {
    this.processing.latency = this.processing.processedAt - this.processing.receivedAt;
  }
  
  // Set default retention if not specified
  if (!this.retention.expiresAt) {
    const retentionDays = 365; // Default 1 year
    this.retention.expiresAt = new Date(Date.now() + retentionDays * 24 * 60 * 60 * 1000);
  }
  
  next();
});

// Instance Methods
locationSchema.methods.calculateDistance = function(otherLocation) {
  const R = 6371000; // Earth's radius in meters
  const lat1 = this.location.coordinates[1] * Math.PI / 180;
  const lat2 = otherLocation.location.coordinates[1] * Math.PI / 180;
  const deltaLat = (otherLocation.location.coordinates[1] - this.location.coordinates[1]) * Math.PI / 180;
  const deltaLon = (otherLocation.location.coordinates[0] - this.location.coordinates[0]) * Math.PI / 180;
  
  const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
            Math.cos(lat1) * Math.cos(lat2) *
            Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  
  return R * c; // Distance in meters
};

locationSchema.methods.calculateBearing = function(otherLocation) {
  const lat1 = this.location.coordinates[1] * Math.PI / 180;
  const lat2 = otherLocation.location.coordinates[1] * Math.PI / 180;
  const deltaLon = (otherLocation.location.coordinates[0] - this.location.coordinates[0]) * Math.PI / 180;
  
  const y = Math.sin(deltaLon) * Math.cos(lat2);
  const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLon);
  
  const bearing = Math.atan2(y, x) * 180 / Math.PI;
  return (bearing + 360) % 360; // Normalize to 0-360
};

locationSchema.methods.isInsideGeofence = function(geofence) {
  // Simplified point-in-polygon check
  // In production, use a proper geospatial library
  if (geofence.type === 'circle') {
    const distance = this.calculateDistance({
      location: { coordinates: geofence.center.coordinates }
    });
    return distance <= geofence.radius;
  }
  
  // For polygon geofences, implement ray casting algorithm
  return false; // Placeholder
};

// Static Methods
locationSchema.statics.getLatest = function(deviceId, limit = 1) {
  return this.find({ device: deviceId })
    .sort({ timestamp: -1 })
    .limit(limit);
};

locationSchema.statics.getByTimeRange = function(deviceId, startTime, endTime) {
  return this.find({
    device: deviceId,
    timestamp: {
      $gte: startTime,
      $lte: endTime
    }
  }).sort({ timestamp: 1 });
};

locationSchema.statics.findInArea = function(deviceId, center, radius) {
  return this.find({
    device: deviceId,
    'location.coordinates': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: center
        },
        $maxDistance: radius
      }
    }
  }).sort({ timestamp: -1 });
};

locationSchema.statics.getRouteSegments = function(deviceId, startTime, endTime, maxGap = 300) {
  return this.aggregate([
    {
      $match: {
        device: mongoose.Types.ObjectId(deviceId),
        timestamp: { $gte: startTime, $lte: endTime }
      }
    },
    {
      $sort: { timestamp: 1 }
    },
    {
      $group: {
        _id: {
          $subtract: [
            '$timestamp',
            { $multiply: [{ $mod: [{ $toLong: '$timestamp' }, maxGap * 1000] }, 1] }
          ]
        },
        locations: { $push: '$$ROOT' },
        startTime: { $first: '$timestamp' },
        endTime: { $last: '$timestamp' },
        count: { $sum: 1 }
      }
    },
    {
      $match: { count: { $gte: 2 } }
    }
  ]);
};

locationSchema.statics.getSpeedViolations = function(deviceId, speedLimit, startTime, endTime) {
  return this.find({
    device: deviceId,
    speed: { $gt: speedLimit },
    timestamp: {
      $gte: startTime,
      $lte: endTime
    }
  }).sort({ timestamp: -1 });
};

locationSchema.statics.getAnomalies = function(deviceId, threshold = 0.8, startTime, endTime) {
  return this.find({
    device: deviceId,
    'analysis.anomalyScore': { $gte: threshold },
    timestamp: {
      $gte: startTime,
      $lte: endTime
    }
  }).sort({ 'analysis.anomalyScore': -1 });
};

locationSchema.statics.aggregateByHour = function(deviceId, startTime, endTime) {
  return this.aggregate([
    {
      $match: {
        device: mongoose.Types.ObjectId(deviceId),
        timestamp: { $gte: startTime, $lte: endTime }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' },
          hour: { $hour: '$timestamp' }
        },
        count: { $sum: 1 },
        avgSpeed: { $avg: '$speed' },
        maxSpeed: { $max: '$speed' },
        totalDistance: { $sum: '$movement.distance' },
        avgBattery: { $avg: '$deviceStatus.batteryLevel' }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.hour': 1 }
    }
  ]);
};

module.exports = mongoose.model('Location', locationSchema);
