/**
 * API Utility Class for GPS Tracker Dashboard
 */
class API {
    constructor() {
        this.baseURL = window.location.origin;
        this.token = localStorage.getItem('authToken');
    }

    /**
     * Set authentication token
     * @param {string} token - JWT token
     */
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('authToken', token);
        } else {
            localStorage.removeItem('authToken');
        }
    }

    /**
     * Get authentication headers
     * @returns {Object} Headers object
     */
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    /**
     * Make HTTP request
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise} Response promise
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.getHeaders(),
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    /**
     * GET request
     * @param {string} endpoint - API endpoint
     * @returns {Promise} Response promise
     */
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    /**
     * POST request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Response promise
     */
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Response promise
     */
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE request
     * @param {string} endpoint - API endpoint
     * @returns {Promise} Response promise
     */
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // Authentication endpoints
    async login(email, password) {
        return this.post('/api/auth/login', { email, password });
    }

    async register(userData) {
        return this.post('/api/auth/register', userData);
    }

    async getProfile() {
        return this.get('/api/auth/profile');
    }

    async updateProfile(data) {
        return this.put('/api/auth/profile', data);
    }

    async logout() {
        return this.post('/api/auth/logout');
    }

    // Device endpoints
    async getDevices(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.get(`/api/devices${queryString ? '?' + queryString : ''}`);
    }

    async getDevice(id) {
        return this.get(`/api/devices/${id}`);
    }

    async registerDevice(deviceData) {
        return this.post('/api/devices', deviceData);
    }

    async updateDevice(id, data) {
        return this.put(`/api/devices/${id}`, data);
    }

    async deleteDevice(id) {
        return this.delete(`/api/devices/${id}`);
    }

    async getDeviceStats(id, days = 7) {
        return this.get(`/api/devices/${id}/stats?days=${days}`);
    }

    // Location endpoints
    async getLocationHistory(deviceId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.get(`/api/locations/devices/${deviceId}/history${queryString ? '?' + queryString : ''}`);
    }

    async getCurrentLocation(deviceId) {
        return this.get(`/api/locations/devices/${deviceId}/current`);
    }

    async getLocationsInArea(deviceId, latitude, longitude, radius = 1000) {
        return this.get(`/api/locations/devices/${deviceId}/area?latitude=${latitude}&longitude=${longitude}&radius=${radius}`);
    }

    // AI endpoints
    async getRoutePrediction(deviceId, timeHorizon = 30) {
        return this.get(`/api/ai/devices/${deviceId}/predict-route?timeHorizon=${timeHorizon}`);
    }

    async analyzeAnomaly(deviceId, locationData) {
        return this.post(`/api/ai/devices/${deviceId}/analyze-anomaly`, locationData);
    }

    async getDeviceInsights(deviceId, days = 7) {
        return this.get(`/api/ai/devices/${deviceId}/insights?days=${days}`);
    }

    async getMovementPatterns(deviceId, days = 30) {
        return this.get(`/api/ai/devices/${deviceId}/movement-patterns?days=${days}`);
    }
}

// Create global API instance
window.api = new API();
