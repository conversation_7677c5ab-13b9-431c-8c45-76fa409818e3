#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

/**
 * Cross-platform log tailing script
 */
class LogTailer {
  constructor() {
    this.logsDir = path.join(process.cwd(), 'logs');
    this.logFiles = [
      'app.log',
      'error.log',
      'access.log',
      'auth-service.log',
      'device-service.log',
      'location-service.log',
      'ai-service.log',
      'notification-service.log'
    ];
  }

  async ensureLogsDirectory() {
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
      console.log(`📁 Created logs directory: ${this.logsDir}`);
    }
  }

  getExistingLogFiles() {
    const existingFiles = [];
    
    for (const logFile of this.logFiles) {
      const filePath = path.join(this.logsDir, logFile);
      if (fs.existsSync(filePath)) {
        existingFiles.push(filePath);
      }
    }
    
    return existingFiles;
  }

  async tailLogs() {
    await this.ensureLogsDirectory();
    
    const existingFiles = this.getExistingLogFiles();
    
    if (existingFiles.length === 0) {
      console.log('📝 No log files found. Creating sample log files...');
      
      // Create sample log files
      for (const logFile of this.logFiles.slice(0, 3)) { // Create first 3 files
        const filePath = path.join(this.logsDir, logFile);
        const sampleContent = `[${new Date().toISOString()}] INFO: Log file created - ${logFile}\n`;
        fs.writeFileSync(filePath, sampleContent);
        console.log(`  ✅ Created: ${logFile}`);
      }
      
      existingFiles.push(...this.getExistingLogFiles());
    }

    console.log(`\n📊 Tailing ${existingFiles.length} log files:`);
    existingFiles.forEach(file => {
      console.log(`  📄 ${path.basename(file)}`);
    });
    
    console.log('\n🔄 Starting log tail (Press Ctrl+C to stop)...\n');

    // Use different approaches based on platform
    if (process.platform === 'win32') {
      this.tailLogsWindows(existingFiles);
    } else {
      this.tailLogsUnix(existingFiles);
    }
  }

  tailLogsWindows(files) {
    // For Windows, we'll use a simple file watching approach
    const watchers = [];
    
    files.forEach(file => {
      const fileName = path.basename(file);
      let lastSize = fs.statSync(file).size;
      
      const watcher = fs.watchFile(file, { interval: 1000 }, (curr, prev) => {
        if (curr.size > lastSize) {
          const stream = fs.createReadStream(file, {
            start: lastSize,
            end: curr.size
          });
          
          stream.on('data', (chunk) => {
            const lines = chunk.toString().split('\n').filter(line => line.trim());
            lines.forEach(line => {
              console.log(`[${fileName}] ${line}`);
            });
          });
          
          lastSize = curr.size;
        }
      });
      
      watchers.push(watcher);
      
      // Read last few lines initially
      this.readLastLines(file, fileName, 5);
    });

    // Cleanup on exit
    process.on('SIGINT', () => {
      console.log('\n\n🛑 Stopping log tail...');
      watchers.forEach(watcher => {
        if (watcher && typeof watcher.close === 'function') {
          watcher.close();
        }
      });
      process.exit(0);
    });
  }

  tailLogsUnix(files) {
    // For Unix systems, use the tail command
    const tail = spawn('tail', ['-f', ...files]);
    
    tail.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    tail.stderr.on('data', (data) => {
      console.error(`Error: ${data}`);
    });
    
    tail.on('close', (code) => {
      console.log(`\n🛑 Log tail stopped with code ${code}`);
    });

    // Cleanup on exit
    process.on('SIGINT', () => {
      console.log('\n\n🛑 Stopping log tail...');
      tail.kill('SIGTERM');
      process.exit(0);
    });
  }

  readLastLines(filePath, fileName, numLines = 5) {
    try {
      const data = fs.readFileSync(filePath, 'utf8');
      const lines = data.split('\n').filter(line => line.trim());
      const lastLines = lines.slice(-numLines);
      
      if (lastLines.length > 0) {
        console.log(`\n--- Last ${lastLines.length} lines from ${fileName} ---`);
        lastLines.forEach(line => {
          console.log(`[${fileName}] ${line}`);
        });
      }
    } catch (error) {
      console.error(`Error reading ${fileName}:`, error.message);
    }
  }

  async showLogStats() {
    await this.ensureLogsDirectory();
    
    const existingFiles = this.getExistingLogFiles();
    
    if (existingFiles.length === 0) {
      console.log('📊 No log files found.');
      return;
    }

    console.log('📊 Log File Statistics:\n');
    
    let totalSize = 0;
    
    existingFiles.forEach(file => {
      try {
        const stats = fs.statSync(file);
        const fileName = path.basename(file);
        const sizeKB = (stats.size / 1024).toFixed(2);
        const modified = stats.mtime.toLocaleString();
        
        console.log(`📄 ${fileName}`);
        console.log(`   Size: ${sizeKB} KB`);
        console.log(`   Modified: ${modified}`);
        console.log('');
        
        totalSize += stats.size;
      } catch (error) {
        console.error(`Error reading stats for ${file}:`, error.message);
      }
    });
    
    console.log(`📊 Total log size: ${(totalSize / 1024).toFixed(2)} KB`);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0] || 'tail';

const tailer = new LogTailer();

switch (command) {
  case 'stats':
    tailer.showLogStats();
    break;
  case 'tail':
  default:
    tailer.tailLogs().catch(error => {
      console.error('Log tailing failed:', error);
      process.exit(1);
    });
    break;
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
