const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../utils/logger');

/**
 * Authentication Middleware
 */
class AuthMiddleware {
  /**
   * Verify JWT token and authenticate user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async authenticate(req, res, next) {
    try {
      // Get token from header
      const authHeader = req.header('Authorization');
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          success: false,
          message: 'Access denied. No token provided or invalid format.'
        });
      }
      
      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from database
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid token. User not found.'
        });
      }
      
      // Check if user is active
      if (!user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'Account is deactivated.'
        });
      }
      
      // Check if account is locked
      if (user.isLocked) {
        return res.status(423).json({
          success: false,
          message: 'Account is temporarily locked due to multiple failed login attempts.'
        });
      }
      
      // Add user to request object
      req.user = user;
      next();
      
    } catch (error) {
      logger.error('Authentication error:', error);
      
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: 'Invalid token.'
        });
      }
      
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token expired.'
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Authentication failed.'
      });
    }
  }
  
  /**
   * Optional authentication - doesn't fail if no token provided
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async optionalAuth(req, res, next) {
    try {
      const authHeader = req.header('Authorization');
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next(); // Continue without authentication
      }
      
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select('-password');
      
      if (user && user.isActive && !user.isLocked) {
        req.user = user;
      }
      
      next();
      
    } catch (error) {
      // Log error but continue without authentication
      logger.warn('Optional authentication failed:', error.message);
      next();
    }
  }
  
  /**
   * Authorize user roles
   * @param {...string} roles - Allowed roles
   * @returns {Function} Middleware function
   */
  static authorize(...roles) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required.'
        });
      }
      
      if (!roles.includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions.'
        });
      }
      
      next();
    };
  }
  
  /**
   * Check if user owns the resource or is admin
   * @param {string} resourceField - Field name containing user ID
   * @returns {Function} Middleware function
   */
  static authorizeOwnership(resourceField = 'owner') {
    return async (req, res, next) => {
      try {
        if (!req.user) {
          return res.status(401).json({
            success: false,
            message: 'Authentication required.'
          });
        }
        
        // Admin can access everything
        if (req.user.role === 'admin') {
          return next();
        }
        
        // Get resource ID from params
        const resourceId = req.params.id;
        
        if (!resourceId) {
          return res.status(400).json({
            success: false,
            message: 'Resource ID required.'
          });
        }
        
        // This is a generic middleware, specific ownership checks
        // should be implemented in individual route handlers
        req.checkOwnership = {
          userId: req.user._id,
          resourceField,
          resourceId
        };
        
        next();
        
      } catch (error) {
        logger.error('Authorization error:', error);
        return res.status(500).json({
          success: false,
          message: 'Authorization failed.'
        });
      }
    };
  }
  
  /**
   * Rate limiting for authentication endpoints
   * @param {number} maxAttempts - Maximum attempts allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {Function} Middleware function
   */
  static authRateLimit(maxAttempts = 5, windowMs = 15 * 60 * 1000) {
    const attempts = new Map();
    
    return (req, res, next) => {
      const key = req.ip + ':' + (req.body.email || req.body.username || '');
      const now = Date.now();
      
      // Clean old entries
      for (const [k, v] of attempts.entries()) {
        if (now - v.firstAttempt > windowMs) {
          attempts.delete(k);
        }
      }
      
      const userAttempts = attempts.get(key);
      
      if (!userAttempts) {
        attempts.set(key, { count: 1, firstAttempt: now });
        return next();
      }
      
      if (userAttempts.count >= maxAttempts) {
        const timeLeft = Math.ceil((windowMs - (now - userAttempts.firstAttempt)) / 1000);
        
        return res.status(429).json({
          success: false,
          message: `Too many authentication attempts. Try again in ${timeLeft} seconds.`,
          retryAfter: timeLeft
        });
      }
      
      userAttempts.count++;
      next();
    };
  }
  
  /**
   * Validate API key for device authentication
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async validateApiKey(req, res, next) {
    try {
      const apiKey = req.header('X-API-Key');
      
      if (!apiKey) {
        return res.status(401).json({
          success: false,
          message: 'API key required.'
        });
      }
      
      // In a real application, you would validate the API key against a database
      // For now, we'll use a simple validation
      const Device = require('../models/Device');
      const device = await Device.findOne({ 
        deviceId: apiKey,
        isActive: true 
      }).populate('owner');
      
      if (!device) {
        return res.status(401).json({
          success: false,
          message: 'Invalid API key.'
        });
      }
      
      req.device = device;
      req.user = device.owner;
      next();
      
    } catch (error) {
      logger.error('API key validation error:', error);
      return res.status(500).json({
        success: false,
        message: 'API key validation failed.'
      });
    }
  }
  
  /**
   * Refresh token validation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async validateRefreshToken(req, res, next) {
    try {
      const { refreshToken } = req.body;
      
      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          message: 'Refresh token required.'
        });
      }
      
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
      
      if (decoded.type !== 'refresh') {
        return res.status(401).json({
          success: false,
          message: 'Invalid refresh token type.'
        });
      }
      
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user || !user.isActive || user.isLocked) {
        return res.status(401).json({
          success: false,
          message: 'Invalid refresh token.'
        });
      }
      
      req.user = user;
      next();
      
    } catch (error) {
      logger.error('Refresh token validation error:', error);
      
      if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Invalid or expired refresh token.'
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Refresh token validation failed.'
      });
    }
  }
}

module.exports = AuthMiddleware;
