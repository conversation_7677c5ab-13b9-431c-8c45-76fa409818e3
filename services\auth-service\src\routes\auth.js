const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const config = require('config');
const logger = require('../../../../shared/utils/logger');
const database = require('../../../../shared/utils/database');
const { authenticate, authRateLimit } = require('../../../../shared/middleware/auth');
const User = require('../../../../shared/models/User');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *         twoFactorCode:
 *           type: string
 *         rememberMe:
 *           type: boolean
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - username
 *         - email
 *         - password
 *         - firstName
 *         - lastName
 *       properties:
 *         username:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         phone:
 *           type: string
 *         tenantId:
 *           type: string
 */

// Validation middleware
const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters'),
  body('twoFactorCode')
    .optional()
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('Two-factor code must be 6 digits')
];

const validateRegister = [
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 6 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must be at least 6 characters with uppercase, lowercase, and number'),
  body('firstName')
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('Last name is required and must be less than 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Valid phone number is required')
];

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Validation error or user already exists
 *       500:
 *         description: Internal server error
 */
router.post('/register', authRateLimit(5, 15 * 60 * 1000), validateRegister, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { username, email, password, firstName, lastName, phone, tenantId } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: email.toLowerCase() },
        { username: username.toLowerCase() }
      ]
    });

    if (existingUser) {
      logger.security('Registration attempt with existing credentials', {
        email,
        username,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(400).json({
        success: false,
        message: 'User with this email or username already exists'
      });
    }

    // Create new user
    const userData = {
      username: username.toLowerCase(),
      email: email.toLowerCase(),
      password,
      profile: {
        firstName,
        lastName,
        phone: phone ? { number: phone } : undefined
      }
    };

    // Add tenant if provided
    if (tenantId) {
      userData.tenants = [{
        tenantId,
        role: 'viewer',
        permissions: []
      }];
    }

    const user = new User(userData);
    await user.save();

    // Generate tokens
    const accessToken = user.generateAuthToken();
    const refreshToken = user.generateRefreshToken();

    // Store refresh token in Redis
    const redis = database.getRedis();
    await redis.setex(`refresh_token:${user._id}`, 7 * 24 * 60 * 60, refreshToken);

    // Log successful registration
    logger.business('User registered', {
      userId: user._id,
      username: user.username,
      email: user.email,
      tenantId,
      ip: req.ip
    });

    // Send verification email (implement based on your email service)
    // await emailService.sendVerificationEmail(user);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          profile: user.profile,
          role: user.role,
          tenants: user.tenants,
          isVerified: user.isVerified
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: config.get('auth.jwt.expiresIn')
        }
      }
    });

  } catch (error) {
    logger.error('Registration error', {
      error: error.message,
      stack: error.stack,
      requestBody: { ...req.body, password: '[REDACTED]' }
    });

    res.status(500).json({
      success: false,
      message: 'Registration failed'
    });
  }
});

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 *       423:
 *         description: Account locked
 *       500:
 *         description: Internal server error
 */
router.post('/login', authRateLimit(5, 15 * 60 * 1000), validateLogin, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password, twoFactorCode, rememberMe } = req.body;

    // Find user
    const user = await User.findOne({ 
      email: email.toLowerCase(),
      isActive: true 
    }).select('+password');

    if (!user) {
      logger.security('Login attempt with non-existent email', {
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if account is locked
    if (user.isLocked) {
      logger.security('Login attempt on locked account', {
        userId: user._id,
        email,
        ip: req.ip
      });

      return res.status(423).json({
        success: false,
        message: 'Account is temporarily locked due to too many failed login attempts'
      });
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      // Handle failed login attempt
      await User.handleLoginAttempt(user._id, false);

      logger.security('Failed login attempt - invalid password', {
        userId: user._id,
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check two-factor authentication if enabled
    if (user.security.twoFactorAuth.enabled) {
      if (!twoFactorCode) {
        return res.status(200).json({
          success: false,
          message: 'Two-factor authentication code required',
          requiresTwoFactor: true,
          userId: user._id
        });
      }

      const isValidTwoFactor = speakeasy.totp.verify({
        secret: user.security.twoFactorAuth.secret,
        encoding: 'base32',
        token: twoFactorCode,
        window: 2
      });

      if (!isValidTwoFactor) {
        logger.security('Failed two-factor authentication', {
          userId: user._id,
          email,
          ip: req.ip
        });

        return res.status(401).json({
          success: false,
          message: 'Invalid two-factor authentication code'
        });
      }

      // Update last used time for 2FA
      user.security.twoFactorAuth.lastUsed = new Date();
      await user.save();
    }

    // Successful login - reset login attempts
    await User.handleLoginAttempt(user._id, true);

    // Generate tokens
    const accessToken = user.generateAuthToken();
    const refreshToken = user.generateRefreshToken();

    // Store refresh token in Redis
    const redis = database.getRedis();
    const refreshExpiry = rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60; // 30 days or 7 days
    await redis.setex(`refresh_token:${user._id}`, refreshExpiry, refreshToken);

    // Create session record
    const sessionId = require('uuid').v4();
    const sessionData = {
      sessionId,
      deviceInfo: {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        location: req.get('CF-IPCountry') || 'Unknown'
      },
      createdAt: new Date(),
      lastActivity: new Date(),
      isActive: true
    };

    user.security.activeSessions.push(sessionData);
    await user.save();

    // Log successful login
    logger.business('User logged in', {
      userId: user._id,
      username: user.username,
      email: user.email,
      sessionId,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          profile: user.profile,
          role: user.role,
          tenants: user.tenants.filter(t => t.isActive),
          isVerified: user.isVerified,
          subscription: user.subscription,
          preferences: user.preferences
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: config.get('auth.jwt.expiresIn')
        },
        session: {
          sessionId,
          expiresAt: new Date(Date.now() + refreshExpiry * 1000)
        }
      }
    });

  } catch (error) {
    logger.error('Login error', {
      error: error.message,
      stack: error.stack,
      requestBody: { ...req.body, password: '[REDACTED]' }
    });

    res.status(500).json({
      success: false,
      message: 'Login failed'
    });
  }
});

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 *       500:
 *         description: Internal server error
 */
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token is required'
      });
    }

    // Verify refresh token
    const authConfig = config.get('auth.jwt');
    let payload;
    
    try {
      payload = jwt.verify(refreshToken, authConfig.refreshSecret);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    // Check if refresh token exists in Redis
    const redis = database.getRedis();
    const storedToken = await redis.get(`refresh_token:${payload.sub}`);
    
    if (!storedToken || storedToken !== refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token not found or expired'
      });
    }

    // Get user
    const user = await User.findById(payload.sub);
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    // Generate new tokens
    const newAccessToken = user.generateAuthToken();
    const newRefreshToken = user.generateRefreshToken();

    // Update refresh token in Redis
    await redis.setex(`refresh_token:${user._id}`, 7 * 24 * 60 * 60, newRefreshToken);

    // Delete old refresh token
    await redis.del(`refresh_token:${user._id}`);

    logger.business('Token refreshed', {
      userId: user._id,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresIn: authConfig.expiresIn
        }
      }
    });

  } catch (error) {
    logger.error('Token refresh error', {
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      message: 'Token refresh failed'
    });
  }
});

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/logout', authenticate(), async (req, res) => {
  try {
    const user = req.user;
    const sessionId = req.get('X-Session-ID');

    // Remove refresh token from Redis
    const redis = database.getRedis();
    await redis.del(`refresh_token:${user._id}`);

    // Blacklist current access token
    const token = req.get('Authorization')?.replace('Bearer ', '');
    if (token) {
      const decoded = jwt.decode(token);
      if (decoded && decoded.jti) {
        await redis.setex(`blacklist:${decoded.jti}`, 24 * 60 * 60, 'true');
      }
    }

    // Deactivate session
    if (sessionId) {
      const session = user.security.activeSessions.find(s => s.sessionId === sessionId);
      if (session) {
        session.isActive = false;
        await user.save();
      }
    }

    logger.business('User logged out', {
      userId: user._id,
      sessionId,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    logger.error('Logout error', {
      error: error.message,
      userId: req.user?._id
    });

    res.status(500).json({
      success: false,
      message: 'Logout failed'
    });
  }
});

module.exports = router;
