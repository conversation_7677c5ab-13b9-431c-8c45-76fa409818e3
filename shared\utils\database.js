const mongoose = require('mongoose');
const Redis = require('ioredis');
const config = require('config');
const logger = require('./logger');

/**
 * Enterprise Database Manager with MongoDB and Redis
 */
class DatabaseManager {
  constructor() {
    this.mongodb = null;
    this.redis = null;
    this.redisCluster = null;
    this.isConnected = false;
    this.connectionRetries = 0;
    this.maxRetries = 5;
  }

  /**
   * Initialize all database connections
   */
  async initialize() {
    try {
      await Promise.all([
        this.connectMongoDB(),
        this.connectRedis()
      ]);
      
      this.isConnected = true;
      logger.info('Database connections initialized successfully');
      
      // Setup health monitoring
      this.setupHealthMonitoring();
      
      return true;
    } catch (error) {
      logger.error('Failed to initialize database connections', { error: error.message });
      throw error;
    }
  }

  /**
   * Connect to MongoDB with advanced configuration
   */
  async connectMongoDB() {
    const dbConfig = config.get('database.mongodb');
    
    try {
      // Set mongoose options
      mongoose.set('strictQuery', false);
      
      // Connection event handlers
      mongoose.connection.on('connected', () => {
        logger.info('MongoDB connected successfully', { 
          uri: this.maskConnectionString(dbConfig.uri) 
        });
      });

      mongoose.connection.on('error', (error) => {
        logger.error('MongoDB connection error', { error: error.message });
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        this.handleMongoDisconnection();
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
      });

      // Connect to MongoDB
      await mongoose.connect(dbConfig.uri, dbConfig.options);
      
      this.mongodb = mongoose.connection;
      
      // Setup indexes and sharding if enabled
      if (dbConfig.sharding.enabled) {
        await this.setupSharding();
      }
      
      logger.info('MongoDB connection established', {
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name
      });

    } catch (error) {
      logger.error('MongoDB connection failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Connect to Redis with cluster support
   */
  async connectRedis() {
    const redisConfig = config.get('database.redis');
    
    try {
      if (redisConfig.cluster.enabled) {
        // Redis Cluster connection
        this.redisCluster = new Redis.Cluster(redisConfig.cluster.nodes, {
          redisOptions: {
            password: redisConfig.password,
            keyPrefix: redisConfig.keyPrefix,
            retryDelayOnFailover: redisConfig.retryDelayOnFailover,
            enableReadyCheck: redisConfig.enableReadyCheck,
            maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
            lazyConnect: redisConfig.lazyConnect
          },
          enableOfflineQueue: false,
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3
        });

        this.redis = this.redisCluster;
        
        this.redisCluster.on('connect', () => {
          logger.info('Redis cluster connected');
        });

        this.redisCluster.on('error', (error) => {
          logger.error('Redis cluster error', { error: error.message });
        });

      } else {
        // Single Redis instance
        this.redis = new Redis({
          host: redisConfig.host,
          port: redisConfig.port,
          password: redisConfig.password,
          db: redisConfig.db,
          keyPrefix: redisConfig.keyPrefix,
          retryDelayOnFailover: redisConfig.retryDelayOnFailover,
          enableReadyCheck: redisConfig.enableReadyCheck,
          maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
          lazyConnect: redisConfig.lazyConnect,
          keepAlive: redisConfig.keepAlive,
          family: redisConfig.family
        });

        this.redis.on('connect', () => {
          logger.info('Redis connected', { 
            host: redisConfig.host, 
            port: redisConfig.port 
          });
        });

        this.redis.on('error', (error) => {
          logger.error('Redis connection error', { error: error.message });
        });

        this.redis.on('close', () => {
          logger.warn('Redis connection closed');
        });
      }

      // Test Redis connection
      await this.redis.ping();
      logger.info('Redis connection established successfully');

    } catch (error) {
      logger.error('Redis connection failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Setup MongoDB sharding
   */
  async setupSharding() {
    try {
      const dbConfig = config.get('database.mongodb');
      const admin = this.mongodb.db.admin();
      
      // Enable sharding for database
      await admin.command({
        enableSharding: this.mongodb.name
      });

      logger.info('MongoDB sharding enabled', { 
        database: this.mongodb.name,
        shardKey: dbConfig.sharding.shardKey
      });

    } catch (error) {
      // Sharding might already be enabled
      logger.warn('Sharding setup warning', { error: error.message });
    }
  }

  /**
   * Handle MongoDB disconnection with retry logic
   */
  async handleMongoDisconnection() {
    if (this.connectionRetries < this.maxRetries) {
      this.connectionRetries++;
      const delay = Math.pow(2, this.connectionRetries) * 1000; // Exponential backoff
      
      logger.info(`Attempting MongoDB reconnection (${this.connectionRetries}/${this.maxRetries}) in ${delay}ms`);
      
      setTimeout(async () => {
        try {
          await this.connectMongoDB();
          this.connectionRetries = 0;
        } catch (error) {
          logger.error('MongoDB reconnection failed', { error: error.message });
        }
      }, delay);
    } else {
      logger.error('MongoDB reconnection attempts exhausted');
    }
  }

  /**
   * Setup health monitoring for databases
   */
  setupHealthMonitoring() {
    // MongoDB health check
    setInterval(async () => {
      try {
        await mongoose.connection.db.admin().ping();
        logger.health('mongodb', 'healthy');
      } catch (error) {
        logger.health('mongodb', 'unhealthy', { error: error.message });
      }
    }, 30000); // Every 30 seconds

    // Redis health check
    setInterval(async () => {
      try {
        await this.redis.ping();
        logger.health('redis', 'healthy');
      } catch (error) {
        logger.health('redis', 'unhealthy', { error: error.message });
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Get MongoDB connection
   */
  getMongoDB() {
    if (!this.mongodb) {
      throw new Error('MongoDB not connected');
    }
    return this.mongodb;
  }

  /**
   * Get Redis connection
   */
  getRedis() {
    if (!this.redis) {
      throw new Error('Redis not connected');
    }
    return this.redis;
  }

  /**
   * Execute MongoDB transaction
   */
  async executeTransaction(operations) {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();
      
      const results = [];
      for (const operation of operations) {
        const result = await operation(session);
        results.push(result);
      }
      
      await session.commitTransaction();
      logger.database('transaction', 'multiple', 'commit', results.length, Date.now());
      
      return results;
    } catch (error) {
      await session.abortTransaction();
      logger.error('Transaction failed', { error: error.message });
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Cache operations with Redis
   */
  async cache(key, value, ttl = null) {
    try {
      const cacheConfig = config.get('cache');
      const finalTtl = ttl || cacheConfig.ttl.default;
      
      if (value === undefined) {
        // Get operation
        const start = Date.now();
        const result = await this.redis.get(key);
        const duration = Date.now() - start;
        
        logger.cache('get', key, !!result, duration);
        return result ? JSON.parse(result) : null;
      } else {
        // Set operation
        const start = Date.now();
        const serialized = JSON.stringify(value);
        
        if (finalTtl > 0) {
          await this.redis.setex(key, finalTtl, serialized);
        } else {
          await this.redis.set(key, serialized);
        }
        
        const duration = Date.now() - start;
        logger.cache('set', key, true, duration);
        return true;
      }
    } catch (error) {
      logger.error('Cache operation failed', { 
        key, 
        operation: value === undefined ? 'get' : 'set',
        error: error.message 
      });
      return null;
    }
  }

  /**
   * Delete cache key
   */
  async deleteCacheKey(key) {
    try {
      const result = await this.redis.del(key);
      logger.cache('delete', key, !!result);
      return result;
    } catch (error) {
      logger.error('Cache delete failed', { key, error: error.message });
      return false;
    }
  }

  /**
   * Get database statistics
   */
  async getStats() {
    const stats = {
      mongodb: {},
      redis: {},
      timestamp: new Date().toISOString()
    };

    try {
      // MongoDB stats
      const mongoStats = await mongoose.connection.db.stats();
      stats.mongodb = {
        connected: mongoose.connection.readyState === 1,
        collections: mongoStats.collections,
        dataSize: mongoStats.dataSize,
        indexSize: mongoStats.indexSize,
        objects: mongoStats.objects
      };
    } catch (error) {
      stats.mongodb.error = error.message;
    }

    try {
      // Redis stats
      const redisInfo = await this.redis.info();
      const lines = redisInfo.split('\r\n');
      const redisStats = {};
      
      lines.forEach(line => {
        if (line.includes(':')) {
          const [key, value] = line.split(':');
          redisStats[key] = value;
        }
      });

      stats.redis = {
        connected: this.redis.status === 'ready',
        version: redisStats.redis_version,
        uptime: redisStats.uptime_in_seconds,
        connectedClients: redisStats.connected_clients,
        usedMemory: redisStats.used_memory_human,
        totalCommandsProcessed: redisStats.total_commands_processed
      };
    } catch (error) {
      stats.redis.error = error.message;
    }

    return stats;
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Shutting down database connections...');
    
    try {
      if (this.redis) {
        await this.redis.quit();
        logger.info('Redis connection closed');
      }
      
      if (this.mongodb) {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed');
      }
      
      this.isConnected = false;
      logger.info('Database connections closed successfully');
    } catch (error) {
      logger.error('Error during database shutdown', { error: error.message });
    }
  }

  /**
   * Mask sensitive information in connection strings
   */
  maskConnectionString(uri) {
    return uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
  }

  /**
   * Check if databases are connected
   */
  isHealthy() {
    return this.isConnected && 
           mongoose.connection.readyState === 1 && 
           this.redis && 
           this.redis.status === 'ready';
  }
}

// Create singleton instance
const databaseManager = new DatabaseManager();

module.exports = databaseManager;
