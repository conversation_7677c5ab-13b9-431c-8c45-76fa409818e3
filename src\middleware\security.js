const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const logger = require('../utils/logger');

/**
 * Security Middleware Configuration
 */
class SecurityMiddleware {
  /**
   * Configure CORS settings
   * @returns {Function} CORS middleware
   */
  static configureCORS() {
    const corsOptions = {
      origin: function (origin, callback) {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);
        
        // In production, you should maintain a whitelist of allowed origins
        const allowedOrigins = [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://127.0.0.1:3000',
          process.env.FRONTEND_URL
        ].filter(Boolean);
        
        if (process.env.NODE_ENV === 'development') {
          return callback(null, true);
        }
        
        if (allowedOrigins.indexOf(origin) !== -1) {
          callback(null, true);
        } else {
          logger.warn(`CORS blocked origin: ${origin}`);
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Key',
        'X-Device-ID'
      ],
      exposedHeaders: ['X-Total-Count', 'X-Page-Count']
    };
    
    return cors(corsOptions);
  }
  
  /**
   * Configure Helmet for security headers
   * @returns {Function} Helmet middleware
   */
  static configureHelmet() {
    return helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", "wss:", "ws:"],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
        }
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    });
  }
  
  /**
   * General API rate limiting
   * @returns {Function} Rate limit middleware
   */
  static apiRateLimit() {
    return rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
      message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000) / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
          success: false,
          message: 'Too many requests from this IP, please try again later.'
        });
      }
    });
  }
  
  /**
   * Strict rate limiting for authentication endpoints
   * @returns {Function} Rate limit middleware
   */
  static authRateLimit() {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 attempts per window
      skipSuccessfulRequests: true,
      message: {
        success: false,
        message: 'Too many authentication attempts, please try again later.',
        retryAfter: 900
      },
      handler: (req, res) => {
        logger.warn(`Auth rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
          success: false,
          message: 'Too many authentication attempts, please try again later.'
        });
      }
    });
  }
  
  /**
   * Rate limiting for location updates (device endpoints)
   * @returns {Function} Rate limit middleware
   */
  static locationUpdateRateLimit() {
    return rateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: 60, // 60 updates per minute per device
      keyGenerator: (req) => {
        return req.device?.deviceId || req.ip;
      },
      message: {
        success: false,
        message: 'Too many location updates, please slow down.'
      },
      handler: (req, res) => {
        logger.warn(`Location update rate limit exceeded for device: ${req.device?.deviceId || req.ip}`);
        res.status(429).json({
          success: false,
          message: 'Too many location updates, please slow down.'
        });
      }
    });
  }
  
  /**
   * Input validation and sanitization
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static sanitizeInput(req, res, next) {
    // Remove any potential XSS attempts from string inputs
    const sanitizeString = (str) => {
      if (typeof str !== 'string') return str;
      
      return str
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    };
    
    const sanitizeObject = (obj) => {
      if (obj === null || typeof obj !== 'object') {
        return typeof obj === 'string' ? sanitizeString(obj) : obj;
      }
      
      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }
      
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    };
    
    // Sanitize request body
    if (req.body) {
      req.body = sanitizeObject(req.body);
    }
    
    // Sanitize query parameters
    if (req.query) {
      req.query = sanitizeObject(req.query);
    }
    
    next();
  }
  
  /**
   * Request size limiting
   * @param {string} limit - Size limit (e.g., '10mb')
   * @returns {Function} Middleware function
   */
  static requestSizeLimit(limit = '10mb') {
    return (req, res, next) => {
      const contentLength = parseInt(req.get('content-length'));
      const maxSize = this.parseSize(limit);
      
      if (contentLength && contentLength > maxSize) {
        return res.status(413).json({
          success: false,
          message: `Request entity too large. Maximum size is ${limit}.`
        });
      }
      
      next();
    };
  }
  
  /**
   * Parse size string to bytes
   * @param {string} size - Size string (e.g., '10mb')
   * @returns {number} Size in bytes
   */
  static parseSize(size) {
    const units = {
      b: 1,
      kb: 1024,
      mb: 1024 * 1024,
      gb: 1024 * 1024 * 1024
    };
    
    const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2] || 'b';
    
    return Math.floor(value * units[unit]);
  }
  
  /**
   * Security headers middleware
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static securityHeaders(req, res, next) {
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    // Add custom security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Add API version header
    res.setHeader('X-API-Version', '1.0.0');
    
    next();
  }
  
  /**
   * Request logging middleware
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static requestLogger(req, res, next) {
    const start = Date.now();
    
    // Log request
    logger.info(`${req.method} ${req.originalUrl}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      deviceId: req.device?.deviceId
    });
    
    // Log response when finished
    res.on('finish', () => {
      const duration = Date.now() - start;
      const level = res.statusCode >= 400 ? 'warn' : 'info';
      
      logger[level](`${req.method} ${req.originalUrl} - ${res.statusCode}`, {
        duration: `${duration}ms`,
        ip: req.ip,
        userId: req.user?.id,
        deviceId: req.device?.deviceId
      });
    });
    
    next();
  }
  
  /**
   * Error handling middleware
   * @param {Error} err - Error object
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static errorHandler(err, req, res, next) {
    logger.error('Unhandled error:', {
      error: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userId: req.user?.id
    });
    
    // Don't leak error details in production
    const message = process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message;
    
    res.status(err.status || 500).json({
      success: false,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
  }
}

module.exports = SecurityMiddleware;
