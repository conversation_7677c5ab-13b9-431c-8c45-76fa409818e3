<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI GPS Tracker Dashboard</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-satellite-dish"></i> AI GPS Tracker</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#dashboard" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a></li>
                    <li><a href="#devices" class="nav-link" data-section="devices">
                        <i class="fas fa-mobile-alt"></i> Devices
                    </a></li>
                    <li><a href="#map" class="nav-link" data-section="map">
                        <i class="fas fa-map"></i> Live Map
                    </a></li>
                    <li><a href="#analytics" class="nav-link" data-section="analytics">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a></li>
                    <li><a href="#ai-insights" class="nav-link" data-section="ai-insights">
                        <i class="fas fa-brain"></i> AI Insights
                    </a></li>
                    <li><a href="#alerts" class="nav-link" data-section="alerts">
                        <i class="fas fa-bell"></i> Alerts
                    </a></li>
                    <li><a href="#settings" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i> Settings
                    </a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span id="username">Loading...</span>
                </div>
                <button id="logout-btn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h1 id="page-title">Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="connection-status">
                        <span id="connection-indicator" class="status-indicator offline"></span>
                        <span id="connection-text">Connecting...</span>
                    </div>
                    <div class="notifications">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count" id="notification-count">0</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Content Sections -->
            <div class="content">
                <!-- Dashboard Section -->
                <section id="dashboard-section" class="content-section active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-devices">0</h3>
                                <p>Total Devices</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon online">
                                <i class="fas fa-wifi"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="online-devices">0</h3>
                                <p>Online Devices</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="active-alerts">0</h3>
                                <p>Active Alerts</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon success">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-distance">0 km</h3>
                                <p>Total Distance Today</p>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <h3>Recent Activity</h3>
                            <div id="recent-activity" class="activity-list">
                                <p class="loading">Loading recent activity...</p>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <h3>Device Status</h3>
                            <div id="device-status" class="device-list">
                                <p class="loading">Loading device status...</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Devices Section -->
                <section id="devices-section" class="content-section">
                    <div class="section-header">
                        <h2>Device Management</h2>
                        <button id="add-device-btn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Device
                        </button>
                    </div>
                    <div class="devices-grid" id="devices-grid">
                        <p class="loading">Loading devices...</p>
                    </div>
                </section>

                <!-- Map Section -->
                <section id="map-section" class="content-section">
                    <div class="map-container">
                        <div id="map" class="map"></div>
                        <div class="map-controls">
                            <button id="center-map-btn" class="map-btn">
                                <i class="fas fa-crosshairs"></i> Center Map
                            </button>
                            <button id="toggle-tracking-btn" class="map-btn">
                                <i class="fas fa-play"></i> Start Tracking
                            </button>
                        </div>
                    </div>
                </section>

                <!-- Analytics Section -->
                <section id="analytics-section" class="content-section">
                    <div class="analytics-header">
                        <h2>Analytics Dashboard</h2>
                        <div class="time-filter">
                            <select id="analytics-timeframe">
                                <option value="7">Last 7 days</option>
                                <option value="30">Last 30 days</option>
                                <option value="90">Last 90 days</option>
                            </select>
                        </div>
                    </div>
                    <div class="charts-grid">
                        <div class="chart-card">
                            <h3>Distance Traveled</h3>
                            <canvas id="distance-chart"></canvas>
                        </div>
                        <div class="chart-card">
                            <h3>Speed Analysis</h3>
                            <canvas id="speed-chart"></canvas>
                        </div>
                        <div class="chart-card">
                            <h3>Activity Patterns</h3>
                            <canvas id="activity-chart"></canvas>
                        </div>
                        <div class="chart-card">
                            <h3>Battery Levels</h3>
                            <canvas id="battery-chart"></canvas>
                        </div>
                    </div>
                </section>

                <!-- AI Insights Section -->
                <section id="ai-insights-section" class="content-section">
                    <div class="ai-header">
                        <h2>AI Insights & Predictions</h2>
                        <div class="device-selector">
                            <select id="ai-device-selector">
                                <option value="">Select a device...</option>
                            </select>
                        </div>
                    </div>
                    <div class="ai-grid">
                        <div class="ai-card">
                            <h3><i class="fas fa-route"></i> Route Prediction</h3>
                            <div id="route-prediction" class="ai-content">
                                <p>Select a device to view route predictions</p>
                            </div>
                        </div>
                        <div class="ai-card">
                            <h3><i class="fas fa-exclamation-circle"></i> Anomaly Detection</h3>
                            <div id="anomaly-detection" class="ai-content">
                                <p>Select a device to view anomaly analysis</p>
                            </div>
                        </div>
                        <div class="ai-card">
                            <h3><i class="fas fa-lightbulb"></i> Smart Recommendations</h3>
                            <div id="recommendations" class="ai-content">
                                <p>Select a device to view recommendations</p>
                            </div>
                        </div>
                        <div class="ai-card">
                            <h3><i class="fas fa-chart-pie"></i> Movement Patterns</h3>
                            <div id="movement-patterns" class="ai-content">
                                <p>Select a device to view movement patterns</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Alerts Section -->
                <section id="alerts-section" class="content-section">
                    <div class="alerts-header">
                        <h2>Alert Management</h2>
                        <div class="alert-filters">
                            <select id="alert-status-filter">
                                <option value="">All Alerts</option>
                                <option value="active">Active</option>
                                <option value="acknowledged">Acknowledged</option>
                                <option value="resolved">Resolved</option>
                            </select>
                            <select id="alert-severity-filter">
                                <option value="">All Severities</option>
                                <option value="info">Info</option>
                                <option value="warning">Warning</option>
                                <option value="critical">Critical</option>
                                <option value="emergency">Emergency</option>
                            </select>
                        </div>
                    </div>
                    <div id="alerts-list" class="alerts-list">
                        <p class="loading">Loading alerts...</p>
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settings-section" class="content-section">
                    <div class="settings-container">
                        <h2>Settings</h2>
                        <div class="settings-grid">
                            <div class="settings-card">
                                <h3>Profile Settings</h3>
                                <form id="profile-form">
                                    <div class="form-group">
                                        <label for="first-name">First Name</label>
                                        <input type="text" id="first-name" name="firstName">
                                    </div>
                                    <div class="form-group">
                                        <label for="last-name">Last Name</label>
                                        <input type="text" id="last-name" name="lastName">
                                    </div>
                                    <div class="form-group">
                                        <label for="phone">Phone</label>
                                        <input type="tel" id="phone" name="phone">
                                    </div>
                                    <button type="submit" class="btn btn-primary">Update Profile</button>
                                </form>
                            </div>
                            <div class="settings-card">
                                <h3>Notification Preferences</h3>
                                <form id="notification-form">
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="email-notifications" name="emailNotifications">
                                            Email Notifications
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="push-notifications" name="pushNotifications">
                                            Push Notifications
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="sms-notifications" name="smsNotifications">
                                            SMS Notifications
                                        </label>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Save Preferences</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <h2>Login to AI GPS Tracker</h2>
            <form id="login-form">
                <div class="form-group">
                    <label for="login-email">Email</label>
                    <input type="email" id="login-email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Login</button>
            </form>
            <p class="modal-footer">
                Don't have an account? <a href="#" id="show-register">Register here</a>
            </p>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="register-modal" class="modal">
        <div class="modal-content">
            <h2>Register for AI GPS Tracker</h2>
            <form id="register-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="register-first-name">First Name</label>
                        <input type="text" id="register-first-name" name="firstName" required>
                    </div>
                    <div class="form-group">
                        <label for="register-last-name">Last Name</label>
                        <input type="text" id="register-last-name" name="lastName" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="register-username">Username</label>
                    <input type="text" id="register-username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="register-email">Email</label>
                    <input type="email" id="register-email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="register-password">Password</label>
                    <input type="password" id="register-password" name="password" required>
                </div>
                <div class="form-group">
                    <label for="register-phone">Phone (Optional)</label>
                    <input type="tel" id="register-phone" name="phone">
                </div>
                <button type="submit" class="btn btn-primary btn-full">Register</button>
            </form>
            <p class="modal-footer">
                Already have an account? <a href="#" id="show-login">Login here</a>
            </p>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/map.js"></script>
    <script src="js/analytics.js"></script>
    <script src="js/ai.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
