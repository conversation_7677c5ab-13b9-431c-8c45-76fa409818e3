/**
 * Dashboard Manager
 */
class Dashboard {
    constructor() {
        this.devices = [];
        this.currentSection = 'dashboard';
        this.refreshInterval = null;
        this.socket = null;
    }

    /**
     * Initialize dashboard
     */
    async initialize() {
        if (!auth.isAuthenticated()) {
            return;
        }

        this.bindEvents();
        this.initializeSocket();
        await this.loadInitialData();
        this.startAutoRefresh();
    }

    /**
     * Bind dashboard events
     */
    bindEvents() {
        // Navigation
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
            });
        });

        // Add device button
        const addDeviceBtn = document.getElementById('add-device-btn');
        if (addDeviceBtn) {
            addDeviceBtn.addEventListener('click', () => this.showAddDeviceModal());
        }
    }

    /**
     * Initialize Socket.IO connection
     */
    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateConnectionStatus(false);
        });

        this.socket.on('locationUpdate', (data) => {
            this.handleLocationUpdate(data);
        });
    }

    /**
     * Load initial dashboard data
     */
    async loadInitialData() {
        try {
            await this.loadDevices();
            await this.loadDashboardStats();
            await this.loadRecentActivity();
        } catch (error) {
            console.error('Failed to load initial data:', error);
            auth.showMessage('Failed to load dashboard data', 'error');
        }
    }

    /**
     * Load devices
     */
    async loadDevices() {
        try {
            const response = await api.getDevices();
            this.devices = response.data.devices;
            this.updateDevicesList();
            this.updateDeviceStatus();
        } catch (error) {
            console.error('Failed to load devices:', error);
        }
    }

    /**
     * Load dashboard statistics
     */
    async loadDashboardStats() {
        try {
            const totalDevices = this.devices.length;
            const onlineDevices = this.devices.filter(device => device.isOnline).length;
            
            // Update stat cards
            document.getElementById('total-devices').textContent = totalDevices;
            document.getElementById('online-devices').textContent = onlineDevices;
            
            // Calculate total distance (simplified)
            let totalDistance = 0;
            for (const device of this.devices) {
                try {
                    const stats = await api.getDeviceStats(device._id, 1); // Today only
                    if (stats.success) {
                        totalDistance += parseFloat(stats.data.statistics.totalDistance || 0);
                    }
                } catch (error) {
                    console.error(`Failed to get stats for device ${device._id}:`, error);
                }
            }
            
            document.getElementById('total-distance').textContent = `${totalDistance.toFixed(1)} km`;
            
        } catch (error) {
            console.error('Failed to load dashboard stats:', error);
        }
    }

    /**
     * Load recent activity
     */
    async loadRecentActivity() {
        const activityContainer = document.getElementById('recent-activity');
        if (!activityContainer) return;

        try {
            // This is a simplified version - in a real app you'd have an activity endpoint
            const activities = [];
            
            for (const device of this.devices.slice(0, 5)) {
                try {
                    const location = await api.getCurrentLocation(device._id);
                    if (location.success) {
                        activities.push({
                            type: 'location_update',
                            device: device.name,
                            timestamp: location.data.location.timestamp,
                            message: `Location updated for ${device.name}`
                        });
                    }
                } catch (error) {
                    // Device might not have location data
                }
            }

            // Sort by timestamp
            activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // Render activities
            if (activities.length === 0) {
                activityContainer.innerHTML = '<p class="loading">No recent activity</p>';
            } else {
                activityContainer.innerHTML = activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="activity-content">
                            <p>${activity.message}</p>
                            <small>${this.formatTimestamp(activity.timestamp)}</small>
                        </div>
                    </div>
                `).join('');
            }
        } catch (error) {
            console.error('Failed to load recent activity:', error);
            activityContainer.innerHTML = '<p class="loading">Failed to load activity</p>';
        }
    }

    /**
     * Update devices list
     */
    updateDevicesList() {
        const devicesGrid = document.getElementById('devices-grid');
        if (!devicesGrid) return;

        if (this.devices.length === 0) {
            devicesGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-mobile-alt"></i>
                    <h3>No Devices Found</h3>
                    <p>Add your first GPS tracking device to get started.</p>
                    <button class="btn btn-primary" onclick="dashboard.showAddDeviceModal()">
                        <i class="fas fa-plus"></i> Add Device
                    </button>
                </div>
            `;
            return;
        }

        devicesGrid.innerHTML = this.devices.map(device => `
            <div class="device-card" data-device-id="${device._id}">
                <div class="device-header">
                    <div class="device-info">
                        <h3>${device.name}</h3>
                        <p class="device-id">${device.deviceId}</p>
                    </div>
                    <div class="device-status ${device.isOnline ? 'online' : 'offline'}">
                        <span class="status-dot"></span>
                        ${device.isOnline ? 'Online' : 'Offline'}
                    </div>
                </div>
                <div class="device-details">
                    <div class="detail-item">
                        <i class="fas fa-tag"></i>
                        <span>${device.type}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-battery-half"></i>
                        <span>${device.batteryLevel || 'N/A'}%</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-clock"></i>
                        <span>${device.lastSeen ? this.formatTimestamp(device.lastSeen) : 'Never'}</span>
                    </div>
                </div>
                <div class="device-actions">
                    <button class="btn btn-sm" onclick="dashboard.viewDevice('${device._id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn btn-sm" onclick="dashboard.editDevice('${device._id}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="dashboard.deleteDevice('${device._id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * Update device status in sidebar
     */
    updateDeviceStatus() {
        const deviceStatusContainer = document.getElementById('device-status');
        if (!deviceStatusContainer) return;

        if (this.devices.length === 0) {
            deviceStatusContainer.innerHTML = '<p class="loading">No devices to display</p>';
            return;
        }

        deviceStatusContainer.innerHTML = this.devices.slice(0, 5).map(device => `
            <div class="device-status-item">
                <div class="device-status-info">
                    <span class="device-name">${device.name}</span>
                    <span class="device-type">${device.type}</span>
                </div>
                <div class="device-status-indicator ${device.isOnline ? 'online' : 'offline'}">
                    <span class="status-dot"></span>
                    ${device.isOnline ? 'Online' : 'Offline'}
                </div>
            </div>
        `).join('');
    }

    /**
     * Show specific section
     * @param {string} sectionName - Section to show
     */
    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        // Update page title
        const pageTitle = document.getElementById('page-title');
        if (pageTitle) {
            pageTitle.textContent = this.getSectionTitle(sectionName);
        }

        this.currentSection = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    /**
     * Get section title
     * @param {string} sectionName - Section name
     * @returns {string} Section title
     */
    getSectionTitle(sectionName) {
        const titles = {
            dashboard: 'Dashboard',
            devices: 'Device Management',
            map: 'Live Map',
            analytics: 'Analytics',
            'ai-insights': 'AI Insights',
            alerts: 'Alert Management',
            settings: 'Settings'
        };
        return titles[sectionName] || 'Dashboard';
    }

    /**
     * Load section-specific data
     * @param {string} sectionName - Section name
     */
    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'map':
                if (window.mapManager) {
                    window.mapManager.initialize();
                }
                break;
            case 'analytics':
                if (window.analytics) {
                    window.analytics.loadCharts();
                }
                break;
            case 'ai-insights':
                if (window.aiManager) {
                    window.aiManager.initialize();
                }
                break;
        }
    }

    /**
     * Handle real-time location updates
     * @param {Object} data - Location update data
     */
    handleLocationUpdate(data) {
        console.log('Location update received:', data);
        
        // Update device in list
        const deviceIndex = this.devices.findIndex(d => d.deviceId === data.deviceId);
        if (deviceIndex !== -1) {
            this.devices[deviceIndex].lastSeen = new Date();
            this.devices[deviceIndex].isOnline = true;
            if (data.batteryLevel !== undefined) {
                this.devices[deviceIndex].batteryLevel = data.batteryLevel;
            }
        }

        // Update UI
        this.updateDeviceStatus();
        
        // Update map if visible
        if (this.currentSection === 'map' && window.mapManager) {
            window.mapManager.updateDeviceLocation(data);
        }
    }

    /**
     * Update connection status
     * @param {boolean} connected - Connection status
     */
    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connection-indicator');
        const text = document.getElementById('connection-text');
        
        if (indicator && text) {
            if (connected) {
                indicator.classList.remove('offline');
                indicator.classList.add('online');
                text.textContent = 'Connected';
            } else {
                indicator.classList.remove('online');
                indicator.classList.add('offline');
                text.textContent = 'Disconnected';
            }
        }
    }

    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        // Refresh every 30 seconds
        this.refreshInterval = setInterval(() => {
            if (auth.isAuthenticated()) {
                this.loadDashboardStats();
                this.loadRecentActivity();
            }
        }, 30000);
    }

    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * Format timestamp
     * @param {string} timestamp - ISO timestamp
     * @returns {string} Formatted timestamp
     */
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // Less than 1 minute
            return 'Just now';
        } else if (diff < 3600000) { // Less than 1 hour
            const minutes = Math.floor(diff / 60000);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diff < 86400000) { // Less than 1 day
            const hours = Math.floor(diff / 3600000);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString();
        }
    }

    /**
     * Show add device modal (placeholder)
     */
    showAddDeviceModal() {
        auth.showMessage('Add device functionality coming soon!', 'info');
    }

    /**
     * View device details (placeholder)
     * @param {string} deviceId - Device ID
     */
    viewDevice(deviceId) {
        auth.showMessage('View device functionality coming soon!', 'info');
    }

    /**
     * Edit device (placeholder)
     * @param {string} deviceId - Device ID
     */
    editDevice(deviceId) {
        auth.showMessage('Edit device functionality coming soon!', 'info');
    }

    /**
     * Delete device (placeholder)
     * @param {string} deviceId - Device ID
     */
    deleteDevice(deviceId) {
        if (confirm('Are you sure you want to delete this device?')) {
            auth.showMessage('Delete device functionality coming soon!', 'info');
        }
    }
}

// Create global dashboard instance
window.dashboard = new Dashboard();
