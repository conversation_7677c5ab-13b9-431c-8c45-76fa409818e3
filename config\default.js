module.exports = {
  // Application Configuration
  app: {
    name: 'Enterprise AI GPS Tracker',
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    timezone: process.env.TZ || 'UTC'
  },

  // Database Configuration
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/enterprise-gps-tracker',
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 50,
        minPoolSize: 5,
        maxIdleTimeMS: 30000,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferMaxEntries: 0,
        bufferCommands: false,
        retryWrites: true,
        writeConcern: {
          w: 'majority',
          j: true,
          wtimeout: 10000
        },
        readPreference: 'primaryPreferred',
        readConcern: { level: 'majority' }
      },
      // Sharding configuration
      sharding: {
        enabled: process.env.MONGODB_SHARDING === 'true',
        shardKey: { userId: 1, timestamp: 1 },
        chunks: {
          maxSize: 64, // MB
          balancer: true
        }
      }
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD,
      db: process.env.REDIS_DB || 0,
      keyPrefix: 'gps:',
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
      // Cluster configuration
      cluster: {
        enabled: process.env.REDIS_CLUSTER === 'true',
        nodes: process.env.REDIS_CLUSTER_NODES ? 
          process.env.REDIS_CLUSTER_NODES.split(',') : 
          ['localhost:7000', 'localhost:7001', 'localhost:7002']
      }
    }
  },

  // Microservices Configuration
  services: {
    auth: {
      host: process.env.AUTH_SERVICE_HOST || 'localhost',
      port: process.env.AUTH_SERVICE_PORT || 3001,
      url: process.env.AUTH_SERVICE_URL || 'http://localhost:3001'
    },
    device: {
      host: process.env.DEVICE_SERVICE_HOST || 'localhost',
      port: process.env.DEVICE_SERVICE_PORT || 3002,
      url: process.env.DEVICE_SERVICE_URL || 'http://localhost:3002'
    },
    location: {
      host: process.env.LOCATION_SERVICE_HOST || 'localhost',
      port: process.env.LOCATION_SERVICE_PORT || 3003,
      url: process.env.LOCATION_SERVICE_URL || 'http://localhost:3003'
    },
    ai: {
      host: process.env.AI_SERVICE_HOST || 'localhost',
      port: process.env.AI_SERVICE_PORT || 3004,
      url: process.env.AI_SERVICE_URL || 'http://localhost:3004'
    },
    notification: {
      host: process.env.NOTIFICATION_SERVICE_HOST || 'localhost',
      port: process.env.NOTIFICATION_SERVICE_PORT || 3005,
      url: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005'
    },
    gateway: {
      host: process.env.GATEWAY_HOST || 'localhost',
      port: process.env.GATEWAY_PORT || 3000,
      url: process.env.GATEWAY_URL || 'http://localhost:3000'
    }
  },

  // Authentication & Security
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret',
      refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
      algorithm: 'HS256',
      issuer: 'enterprise-gps-tracker',
      audience: 'gps-clients'
    },
    oauth2: {
      google: {
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: process.env.GOOGLE_CALLBACK_URL || '/auth/google/callback'
      },
      microsoft: {
        clientId: process.env.MICROSOFT_CLIENT_ID,
        clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
        callbackURL: process.env.MICROSOFT_CALLBACK_URL || '/auth/microsoft/callback'
      }
    },
    apiKeys: {
      enabled: true,
      headerName: 'X-API-Key',
      queryParam: 'apiKey',
      encryption: {
        algorithm: 'aes-256-gcm',
        key: process.env.API_KEY_ENCRYPTION_KEY
      }
    },
    rbac: {
      enabled: true,
      roles: ['admin', 'manager', 'operator', 'viewer', 'device'],
      permissions: {
        admin: ['*'],
        manager: ['read:*', 'write:devices', 'write:users', 'write:geofences'],
        operator: ['read:*', 'write:devices', 'write:geofences'],
        viewer: ['read:*'],
        device: ['write:locations', 'read:device']
      }
    }
  },

  // Rate Limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // requests per window
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    keyGenerator: (req) => req.ip,
    // Different limits for different endpoints
    endpoints: {
      '/api/auth/login': { windowMs: 15 * 60 * 1000, max: 5 },
      '/api/locations/update': { windowMs: 60 * 1000, max: 3600 }, // 1 per second
      '/api/devices': { windowMs: 15 * 60 * 1000, max: 100 }
    }
  },

  // Real-time Configuration
  realtime: {
    socketio: {
      cors: {
        origin: process.env.CORS_ORIGIN || '*',
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
      upgradeTimeout: 10000,
      maxHttpBufferSize: 1e6,
      allowEIO3: true
    },
    redis: {
      adapter: true,
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379
    }
  },

  // AI Configuration
  ai: {
    models: {
      routePrediction: {
        algorithm: 'lstm',
        lookbackWindow: 10,
        predictionHorizon: 30,
        updateInterval: 300000, // 5 minutes
        minDataPoints: 20
      },
      anomalyDetection: {
        algorithm: 'isolation_forest',
        threshold: 0.8,
        features: ['speed', 'location', 'time', 'route'],
        windowSize: 100,
        updateInterval: 60000 // 1 minute
      },
      behaviorAnalysis: {
        algorithm: 'clustering',
        clusterCount: 5,
        features: ['time_patterns', 'location_patterns', 'speed_patterns'],
        updateInterval: 86400000 // 24 hours
      }
    },
    tensorflow: {
      backend: 'cpu',
      threads: process.env.TF_NUM_THREADS || 4,
      memory: {
        growth: true,
        limit: process.env.TF_MEMORY_LIMIT || 1024
      }
    }
  },

  // Geospatial Configuration
  geospatial: {
    defaultRadius: 100, // meters
    maxRadius: 100000, // 100km
    precision: 6, // decimal places
    srid: 4326, // WGS84
    indexing: {
      type: '2dsphere',
      background: true,
      sparse: true
    },
    clustering: {
      algorithm: 'dbscan',
      epsilon: 100, // meters
      minPoints: 3
    }
  },

  // Message Queue Configuration
  queue: {
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      db: 1
    },
    jobs: {
      locationProcessing: {
        concurrency: 10,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      },
      aiAnalysis: {
        concurrency: 5,
        attempts: 2,
        backoff: {
          type: 'fixed',
          delay: 5000
        }
      },
      notifications: {
        concurrency: 20,
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 1000
        }
      }
    }
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    transports: {
      console: {
        enabled: true,
        level: 'debug',
        colorize: true
      },
      file: {
        enabled: process.env.NODE_ENV === 'production',
        level: 'info',
        filename: 'logs/app.log',
        maxsize: ********, // 10MB
        maxFiles: 5,
        tailable: true
      },
      elasticsearch: {
        enabled: process.env.ELASTICSEARCH_ENABLED === 'true',
        level: 'info',
        index: 'gps-tracker-logs',
        host: process.env.ELASTICSEARCH_HOST || 'localhost:9200'
      }
    }
  },

  // Monitoring Configuration
  monitoring: {
    metrics: {
      enabled: true,
      port: process.env.METRICS_PORT || 9090,
      path: '/metrics',
      collectDefaultMetrics: true,
      customMetrics: {
        locationUpdates: true,
        apiRequests: true,
        aiPredictions: true,
        alerts: true
      }
    },
    tracing: {
      enabled: process.env.TRACING_ENABLED === 'true',
      jaeger: {
        endpoint: process.env.JAEGER_ENDPOINT || 'http://localhost:14268/api/traces',
        serviceName: 'gps-tracker'
      }
    },
    health: {
      enabled: true,
      path: '/health',
      checks: {
        mongodb: true,
        redis: true,
        services: true,
        memory: true,
        disk: true
      }
    }
  },

  // Notification Configuration
  notifications: {
    email: {
      enabled: process.env.EMAIL_ENABLED === 'true',
      service: process.env.EMAIL_SERVICE || 'gmail',
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT || 587,
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      templates: {
        path: 'templates/email',
        engine: 'handlebars'
      }
    },
    sms: {
      enabled: process.env.SMS_ENABLED === 'true',
      provider: process.env.SMS_PROVIDER || 'twilio',
      twilio: {
        accountSid: process.env.TWILIO_ACCOUNT_SID,
        authToken: process.env.TWILIO_AUTH_TOKEN,
        from: process.env.TWILIO_FROM_NUMBER
      }
    },
    push: {
      enabled: process.env.PUSH_ENABLED === 'true',
      firebase: {
        serverKey: process.env.FIREBASE_SERVER_KEY,
        senderId: process.env.FIREBASE_SENDER_ID
      }
    },
    webhook: {
      enabled: process.env.WEBHOOK_ENABLED === 'true',
      timeout: 5000,
      retries: 3
    }
  },

  // Cache Configuration
  cache: {
    ttl: {
      default: 300, // 5 minutes
      user: 1800, // 30 minutes
      device: 900, // 15 minutes
      location: 60, // 1 minute
      ai: 3600 // 1 hour
    },
    keyPrefix: 'cache:',
    compression: true
  },

  // File Storage Configuration
  storage: {
    local: {
      enabled: true,
      path: 'uploads/',
      maxSize: ******** // 10MB
    },
    s3: {
      enabled: process.env.AWS_S3_ENABLED === 'true',
      bucket: process.env.AWS_S3_BUCKET,
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  },

  // Performance Configuration
  performance: {
    compression: {
      enabled: true,
      level: 6,
      threshold: 1024
    },
    clustering: {
      enabled: process.env.CLUSTER_ENABLED === 'true',
      workers: process.env.CLUSTER_WORKERS || require('os').cpus().length
    },
    gracefulShutdown: {
      timeout: 30000 // 30 seconds
    }
  }
};
