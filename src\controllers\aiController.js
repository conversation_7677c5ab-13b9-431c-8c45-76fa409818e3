const aiService = require('../services/aiService');
const Device = require('../models/Device');
const Location = require('../models/Location');
const logger = require('../utils/logger');

/**
 * AI Controller for GPS Tracking Intelligence
 */
class AIController {
  /**
   * Get route prediction for a device
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getRoutePrediction(req, res) {
    try {
      const { id: deviceId } = req.params;
      const { timeHorizon = 30 } = req.query;
      const userId = req.user._id;
      
      // Verify device ownership
      const device = await Device.findOne({
        _id: deviceId,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      // Get route prediction
      const prediction = await aiService.predictNextLocation(deviceId, parseInt(timeHorizon));
      
      res.json({
        success: prediction.success,
        message: prediction.message || 'Route prediction generated',
        data: {
          device: {
            id: device._id,
            name: device.name,
            deviceId: device.deviceId
          },
          prediction: prediction.prediction,
          metadata: prediction.metadata
        }
      });
      
    } catch (error) {
      logger.error('Route prediction error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate route prediction'
      });
    }
  }
  
  /**
   * Analyze location for anomalies
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async analyzeLocationAnomaly(req, res) {
    try {
      const { id: deviceId } = req.params;
      const { latitude, longitude, speed, heading, timestamp } = req.body;
      const userId = req.user._id;
      
      // Verify device ownership
      const device = await Device.findOne({
        _id: deviceId,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      // Prepare location data for analysis
      const locationData = {
        latitude,
        longitude,
        speed,
        heading,
        timestamp: timestamp ? new Date(timestamp) : new Date()
      };
      
      // Detect anomalies
      const anomalyResult = await aiService.detectAnomalies(deviceId, locationData);
      
      res.json({
        success: true,
        message: 'Anomaly analysis completed',
        data: {
          device: {
            id: device._id,
            name: device.name,
            deviceId: device.deviceId
          },
          location: locationData,
          analysis: anomalyResult
        }
      });
      
    } catch (error) {
      logger.error('Anomaly analysis error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to analyze location anomaly'
      });
    }
  }
  
  /**
   * Get AI insights for a device
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getDeviceInsights(req, res) {
    try {
      const { id: deviceId } = req.params;
      const { days = 7 } = req.query;
      const userId = req.user._id;
      
      // Verify device ownership
      const device = await Device.findOne({
        _id: deviceId,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - parseInt(days) * 24 * 60 * 60 * 1000);
      
      // Get location data for analysis
      const locations = await Location.find({
        device: deviceId,
        timestamp: { $gte: startTime, $lte: endTime }
      }).sort({ timestamp: 1 });
      
      if (locations.length === 0) {
        return res.json({
          success: true,
          message: 'No location data available for insights',
          data: {
            device: {
              id: device._id,
              name: device.name,
              deviceId: device.deviceId
            },
            insights: {
              dataPoints: 0,
              message: 'Insufficient data for insights'
            }
          }
        });
      }
      
      // Generate insights
      const insights = await AIController.generateInsights(locations, device);
      
      res.json({
        success: true,
        message: 'Device insights generated',
        data: {
          device: {
            id: device._id,
            name: device.name,
            deviceId: device.deviceId
          },
          period: {
            days: parseInt(days),
            startDate: startTime,
            endDate: endTime
          },
          insights
        }
      });
      
    } catch (error) {
      logger.error('Device insights error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate device insights'
      });
    }
  }
  
  /**
   * Get movement patterns for a device
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getMovementPatterns(req, res) {
    try {
      const { id: deviceId } = req.params;
      const { days = 30 } = req.query;
      const userId = req.user._id;
      
      // Verify device ownership
      const device = await Device.findOne({
        _id: deviceId,
        owner: userId
      });
      
      if (!device) {
        return res.status(404).json({
          success: false,
          message: 'Device not found'
        });
      }
      
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - parseInt(days) * 24 * 60 * 60 * 1000);
      
      // Analyze movement patterns
      const patterns = await AIController.analyzeMovementPatterns(deviceId, startTime, endTime);
      
      res.json({
        success: true,
        message: 'Movement patterns analyzed',
        data: {
          device: {
            id: device._id,
            name: device.name,
            deviceId: device.deviceId
          },
          period: {
            days: parseInt(days),
            startDate: startTime,
            endDate: endTime
          },
          patterns
        }
      });
      
    } catch (error) {
      logger.error('Movement patterns error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to analyze movement patterns'
      });
    }
  }
  
  /**
   * Generate comprehensive insights from location data
   * @param {Array} locations - Location data
   * @param {Object} device - Device object
   * @returns {Object} Generated insights
   */
  static async generateInsights(locations, device) {
    const insights = {
      dataPoints: locations.length,
      timeRange: {
        start: locations[0]?.timestamp,
        end: locations[locations.length - 1]?.timestamp
      },
      movement: {},
      behavior: {},
      efficiency: {},
      recommendations: []
    };
    
    // Movement analysis
    const speeds = locations.map(loc => loc.speed || 0).filter(speed => speed > 0);
    const distances = locations.map(loc => loc.movement?.distance || 0).filter(dist => dist > 0);
    
    if (speeds.length > 0) {
      insights.movement = {
        averageSpeed: (speeds.reduce((a, b) => a + b, 0) / speeds.length).toFixed(2),
        maxSpeed: Math.max(...speeds).toFixed(2),
        minSpeed: Math.min(...speeds).toFixed(2),
        totalDistance: (distances.reduce((a, b) => a + b, 0) / 1000).toFixed(2), // km
        movingTime: speeds.length * 5 // Assuming 5-minute intervals
      };
    }
    
    // Behavior analysis
    const hourlyActivity = new Array(24).fill(0);
    const dailyActivity = new Array(7).fill(0);
    
    locations.forEach(loc => {
      const hour = new Date(loc.timestamp).getHours();
      const day = new Date(loc.timestamp).getDay();
      hourlyActivity[hour]++;
      dailyActivity[day]++;
    });
    
    const mostActiveHour = hourlyActivity.indexOf(Math.max(...hourlyActivity));
    const mostActiveDay = dailyActivity.indexOf(Math.max(...dailyActivity));
    
    insights.behavior = {
      mostActiveHour,
      mostActiveDay,
      activityDistribution: {
        hourly: hourlyActivity,
        daily: dailyActivity
      }
    };
    
    // Efficiency analysis
    if (distances.length > 0 && speeds.length > 0) {
      const totalTime = locations.length * 5 / 60; // hours
      const totalDistance = distances.reduce((a, b) => a + b, 0) / 1000; // km
      const averageSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
      
      insights.efficiency = {
        fuelEfficiencyScore: AIController.calculateFuelEfficiency(averageSpeed, totalDistance),
        routeOptimizationScore: AIController.calculateRouteOptimization(locations),
        idleTimePercentage: AIController.calculateIdleTime(speeds)
      };
    }
    
    // Generate recommendations
    insights.recommendations = AIController.generateRecommendations(insights);
    
    return insights;
  }
  
  /**
   * Analyze movement patterns
   * @param {string} deviceId - Device ID
   * @param {Date} startTime - Start time
   * @param {Date} endTime - End time
   * @returns {Object} Movement patterns
   */
  static async analyzeMovementPatterns(deviceId, startTime, endTime) {
    const locations = await Location.find({
      device: deviceId,
      timestamp: { $gte: startTime, $lte: endTime }
    }).sort({ timestamp: 1 });
    
    const patterns = {
      frequentLocations: [],
      routePatterns: [],
      timePatterns: {},
      anomalousEvents: []
    };
    
    if (locations.length < 10) {
      return {
        ...patterns,
        message: 'Insufficient data for pattern analysis'
      };
    }
    
    // Find frequent locations (clustering)
    patterns.frequentLocations = AIController.findFrequentLocations(locations);
    
    // Analyze route patterns
    patterns.routePatterns = AIController.analyzeRoutePatterns(locations);
    
    // Time-based patterns
    patterns.timePatterns = AIController.analyzeTimePatterns(locations);
    
    return patterns;
  }
  
  /**
   * Find frequent locations using simple clustering
   * @param {Array} locations - Location data
   * @returns {Array} Frequent locations
   */
  static findFrequentLocations(locations) {
    const clusters = [];
    const radius = 100; // 100 meters
    
    locations.forEach(loc => {
      const coord = [loc.location.coordinates[0], loc.location.coordinates[1]];
      
      // Find existing cluster
      let foundCluster = false;
      for (const cluster of clusters) {
        const distance = aiService.calculateDistance(coord, cluster.center);
        if (distance <= radius) {
          cluster.locations.push(loc);
          foundCluster = true;
          break;
        }
      }
      
      // Create new cluster
      if (!foundCluster) {
        clusters.push({
          center: coord,
          locations: [loc]
        });
      }
    });
    
    // Return clusters with more than 5 locations
    return clusters
      .filter(cluster => cluster.locations.length >= 5)
      .map(cluster => ({
        center: cluster.center,
        visitCount: cluster.locations.length,
        averageDwellTime: cluster.locations.length * 5, // Simplified
        significance: cluster.locations.length / locations.length
      }))
      .sort((a, b) => b.visitCount - a.visitCount)
      .slice(0, 10); // Top 10 frequent locations
  }
  
  /**
   * Analyze route patterns
   * @param {Array} locations - Location data
   * @returns {Array} Route patterns
   */
  static analyzeRoutePatterns(locations) {
    // Simplified route pattern analysis
    const routes = [];
    
    // Group locations by day
    const dailyRoutes = {};
    locations.forEach(loc => {
      const day = new Date(loc.timestamp).toDateString();
      if (!dailyRoutes[day]) {
        dailyRoutes[day] = [];
      }
      dailyRoutes[day].push(loc);
    });
    
    // Analyze each day's route
    Object.values(dailyRoutes).forEach(dayLocations => {
      if (dayLocations.length >= 5) {
        const route = {
          date: dayLocations[0].timestamp.toDateString(),
          startLocation: dayLocations[0].location.coordinates,
          endLocation: dayLocations[dayLocations.length - 1].location.coordinates,
          waypoints: dayLocations.length,
          totalDistance: dayLocations.reduce((sum, loc) => sum + (loc.movement?.distance || 0), 0)
        };
        routes.push(route);
      }
    });
    
    return routes.slice(0, 10); // Return last 10 routes
  }
  
  /**
   * Analyze time-based patterns
   * @param {Array} locations - Location data
   * @returns {Object} Time patterns
   */
  static analyzeTimePatterns(locations) {
    const patterns = {
      peakHours: [],
      weekdayVsWeekend: { weekday: 0, weekend: 0 },
      monthlyTrends: {}
    };
    
    const hourlyActivity = new Array(24).fill(0);
    
    locations.forEach(loc => {
      const date = new Date(loc.timestamp);
      const hour = date.getHours();
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;
      
      hourlyActivity[hour]++;
      
      if (isWeekend) {
        patterns.weekdayVsWeekend.weekend++;
      } else {
        patterns.weekdayVsWeekend.weekday++;
      }
    });
    
    // Find peak hours (top 3)
    const hourlyWithIndex = hourlyActivity.map((count, hour) => ({ hour, count }));
    patterns.peakHours = hourlyWithIndex
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour);
    
    return patterns;
  }
  
  /**
   * Calculate fuel efficiency score
   * @param {number} averageSpeed - Average speed
   * @param {number} totalDistance - Total distance
   * @returns {number} Efficiency score (0-100)
   */
  static calculateFuelEfficiency(averageSpeed, totalDistance) {
    // Simplified fuel efficiency calculation
    // Optimal speed range is typically 50-80 km/h
    let score = 100;
    
    if (averageSpeed < 30 || averageSpeed > 100) {
      score -= 30;
    } else if (averageSpeed >= 50 && averageSpeed <= 80) {
      score += 10;
    }
    
    return Math.max(0, Math.min(100, score));
  }
  
  /**
   * Calculate route optimization score
   * @param {Array} locations - Location data
   * @returns {number} Optimization score (0-100)
   */
  static calculateRouteOptimization(locations) {
    // Simplified route optimization score
    // Based on directness of routes
    if (locations.length < 2) return 50;
    
    const start = locations[0].location.coordinates;
    const end = locations[locations.length - 1].location.coordinates;
    
    const directDistance = aiService.calculateDistance(start, end);
    const actualDistance = locations.reduce((sum, loc) => sum + (loc.movement?.distance || 0), 0);
    
    if (actualDistance === 0) return 50;
    
    const efficiency = (directDistance / actualDistance) * 100;
    return Math.min(100, efficiency);
  }
  
  /**
   * Calculate idle time percentage
   * @param {Array} speeds - Speed data
   * @returns {number} Idle time percentage
   */
  static calculateIdleTime(speeds) {
    if (speeds.length === 0) return 0;
    
    const idleCount = speeds.filter(speed => speed < 5).length; // < 5 km/h considered idle
    return ((idleCount / speeds.length) * 100).toFixed(1);
  }
  
  /**
   * Generate recommendations based on insights
   * @param {Object} insights - Generated insights
   * @returns {Array} Recommendations
   */
  static generateRecommendations(insights) {
    const recommendations = [];
    
    if (insights.efficiency?.fuelEfficiencyScore < 70) {
      recommendations.push({
        type: 'efficiency',
        priority: 'medium',
        message: 'Consider optimizing driving speed for better fuel efficiency'
      });
    }
    
    if (insights.efficiency?.routeOptimizationScore < 60) {
      recommendations.push({
        type: 'route',
        priority: 'high',
        message: 'Route optimization could reduce travel time and distance'
      });
    }
    
    if (parseFloat(insights.efficiency?.idleTimePercentage) > 20) {
      recommendations.push({
        type: 'idle',
        priority: 'medium',
        message: 'High idle time detected. Consider reducing unnecessary stops'
      });
    }
    
    return recommendations;
  }
}

module.exports = AIController;
